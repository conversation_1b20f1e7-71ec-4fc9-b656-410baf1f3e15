#FROM --platform=linux/amd64 surnet/alpine-wkhtmltopdf:3.18.0-0.12.6-full as wkhtmltopdf
FROM --platform=linux/amd64 erseco/alpine-php-webserver

ARG BUILD_ENV=dev
ENV APP_ENV=$BUILD_ENV \
    client_max_body_size=2M \
    clear_env=no \
    allow_url_fopen=On \
    allow_url_include=Off \
    display_errors=Off \
    file_uploads=On \
    max_execution_time=0 \
    max_input_time=-1 \
    max_input_vars=1000 \
    memory_limit=128M \
    post_max_size=8M \
    upload_max_filesize=2M \
    zlib_output_compression=On


USER root
RUN apk add --no-cache composer


## Add Composer
#RUN curl -s https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin/ --filename=composer
##RUN composer global require hirak/prestissimo
#ENV COMPOSER_ALLOW_SUPERUSER=1
#ENV PATH="./vendor/bin:$PATH"

### ! wkhtmltopdf
## Install dependencies for wkhtmltopdf
#RUN apk add --no-cache \
#    libstdc++ \
#    libx11 \
#    libxrender \
#    libxext \
#    libssl1.1 \
#    ca-certificates \
#    fontconfig \
#    freetype \
#    ttf-dejavu \
#    ttf-droid \
#    ttf-freefont \
#    ttf-liberation \
#    # more fonts
#  && apk add --no-cache --virtual .build-deps \
#    msttcorefonts-installer \
#  # Install microsoft fonts
#  && update-ms-fonts \
#  && fc-cache -f \
#  # Clean up when done
#  && rm -rf /tmp/* \
#  && apk del .build-deps
#
## Copy wkhtmltopdf files from docker-wkhtmltopdf image
#COPY --from=wkhtmltopdf /bin/wkhtmltopdf /bin/wkhtmltopdf
#COPY --from=wkhtmltopdf /bin/wkhtmltoimage /bin/wkhtmltoimage
#COPY --from=wkhtmltopdf /bin/libwkhtmltox* /bin/

#USER nobody

# ? --build-arg BUILD_ENV=dev|prod


## ! Essentials
RUN echo "Europe/Zagreb" > /etc/timezone
COPY docker/openssl.cnf /etc/ssl/openssl.cnf

WORKDIR /var/www/html
RUN rm -rf *
COPY --chown=nobody . ./
RUN rm -Rf docker/ && ls -lh

USER nobody
RUN composer install --optimize-autoloader --no-interaction
