#!/bin/sh
set -e

#shared volume
mkdir -p /var/data/exports
chmod -R g+w /var/data/exports 2>/dev/null
chgrp -R www-data /var/data/exports 2>/dev/null
[ ! -d public/exports ] || rm -rf public/exports
ln -s /var/data/exports/ public/

# fix perms
chmod -R g+w var 2>/dev/null
chgrp -R www-data var 2>/dev/null
chmod -R g+w public 2>/dev/null
chgrp -R www-data public 2>/dev/null

setfacl -R -m u:www-data:rwX -m u:"$(whoami)":rwX var
setfacl -dR -m u:www-data:rwX -m u:"$(whoami)":rwX var
setfacl -R -m u:www-data:rwX -m u:"$(whoami)":rwX public
setfacl -dR -m u:www-data:rwX -m u:"$(whoami)":rwX public

# --- PROD --- #
if [ "$APP_ENV" = 'prod' ]; then
  #  echo "Decriptying secrets"
  composer dump-autoload --classmap-authoritative --no-dev --optimize
  APP_RUNTIME_ENV=prod php bin/console secrets:decrypt-to-local --force
  composer run-script --no-dev post-install-cmd
  APP_ENV=prod APP_DEBUG=0 php bin/console cache:clear
  php bin/console assets:install

  chown -R www-data:www-data /var/lib/nginx/
  #  exec multirun "php-fpm -F" "nginx -c /etc/nginx/nginx.conf -g 'daemon off;'"
  exec multirun "supervisord --nodaemon --configuration /etc/supervisord.conf" "php-fpm -F" "nginx -c /etc/nginx/nginx.conf -g 'daemon off;'"

# --- DEV --- #
else
  # if no .env.local assume remote env is used and decrypt prod secrets to use prod db

  ### Commenting this block out ensures that no secrets are decrypted, and the application uses the configurations from the .env file or other environment variables
  #if [ ! -f .env.local ]; then
   APP_RUNTIME_ENV=prod php bin/console secrets:decrypt-to-local --force
  #fi

  composer run-script post-install-cmd
  #  bin/console doctrine:migrations:migrate --no-interaction
  bin/console cache:clear
  echo "DEBUG ENV"
  bin/console debug:dotenv

  #  # symfony-cli
  #  wget https://get.symfony.com/cli/installer -O - | bash
  #  #  export PATH="$HOME/.symfony5/bin:$PATH"
  #  mv /root/.symfony5/bin/symfony /usr/local/bin/symfony
  #  symfony serve --port=80 # -d

  chown -R www-data:www-data /var/lib/nginx/
  exec multirun "php-fpm -F" "nginx -c /etc/nginx/nginx.conf -g 'daemon off;'"

fi

# ----- #
#exec multirun "supervisord --nodaemon --configuration /etc/supervisord.conf" "php-fpm -F" "nginx -c /etc/nginx/nginx.conf -g 'daemon off;'"
#exec multirun "php-fpm -F" "nginx -c /etc/nginx/nginx.conf -g 'daemon off;'"
