# 101924221316.dkr.ecr.eu-central-1.amazonaws.com/vt-symfony:latest
FROM --platform=linux/amd64 surnet/alpine-wkhtmltopdf:3.18.0-0.12.6-full as wkhtmltopdf
FROM --platform=linux/amd64 php:8.2-fpm-alpine

RUN apk add --no-cache bash curl nginx multirun acl git

# ! INSTALL PHP EXTENSIONS
# php extensions installer: https://github.com/mlocati/docker-php-extension-installer
COPY --from=mlocati/php-extension-installer:latest --link /usr/bin/install-php-extensions /usr/local/bin/
RUN set -eux; \
    install-php-extensions \
		apcu \
		intl \
		opcache \
		zip \
        pdo \
        pdo_mysql \
        iconv \
        xsl \
        gd \
    ;

# Add Composer
RUN curl -s https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin/ --filename=composer
#RUN composer global require hirak/prestissimo
ENV COMPOSER_ALLOW_SUPERUSER=1
ENV PATH="./vendor/bin:$PATH"

## ! wkhtmltopdf
# Install dependencies for wkhtmltopdf
RUN apk add --no-cache \
    libstdc++ \
    libx11 \
    libxrender \
    libxext \
    libssl1.1 \
    ca-certificates \
    fontconfig \
    freetype \
    ttf-dejavu \
    ttf-droid \
    ttf-freefont \
    ttf-liberation \
    # more fonts
  && apk add --no-cache --virtual .build-deps \
    msttcorefonts-installer \
  # Install microsoft fonts
  && update-ms-fonts \
  && fc-cache -f \
  # Clean up when done
  && rm -rf /tmp/* \
  && apk del .build-deps

# Copy wkhtmltopdf files from docker-wkhtmltopdf image
COPY --from=wkhtmltopdf /bin/wkhtmltopdf /bin/wkhtmltopdf
COPY --from=wkhtmltopdf /bin/wkhtmltoimage /bin/wkhtmltoimage
COPY --from=wkhtmltopdf /bin/libwkhtmltox* /bin/

## Build s3fs
#ENV IAM_ROLE=none
#ENV AWS_ACCESS_KEY_ID=value
#ENV AWS_SECRET_ACCESS_KEY=value
#ENV S3_BUCKET_ACL=private
#ENV S3_BUCKET_NAME=none
#
#VOLUME /mnt/s3-bucket
#
#ARG S3FS_VERSION=v1.93
#
#RUN apk \
#    --update add \
#    --virtual build-dependencies \
#    build-base alpine-sdk \
#    fuse fuse-dev \
#    automake autoconf git \
#    curl-dev libxml2-dev  \
#    ca-certificates
#
#
#RUN set -eux; \
#    cd /var; \
#    git clone https://github.com/s3fs-fuse/s3fs-fuse.git; \
#    cd s3fs-fuse; \
#    sh autogen.sh; \
#    sh configure --prefix=/usr; \
#    make && make install;
#
#
#
##    ./autogen.sh && ./configure \
##    make && make install
##
##RUN echo "----------- s3fs -----------"
##RUN s3fs --version
#
#
## cleanup
## RUN apk del .build-dependencies
#
#ENTRYPOINT ["tail", "-f", "/dev/null"]