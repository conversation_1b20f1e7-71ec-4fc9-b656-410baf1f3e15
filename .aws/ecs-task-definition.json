{"containerDefinitions": [{"name": "noxy-container", "image": "101924221316.dkr.ecr.eu-central-1.amazonaws.com/noxy:latest", "cpu": 512, "memory": 1024, "portMappings": [{"containerPort": 4000, "hostPort": 4000, "protocol": "tcp"}, {"containerPort": 4002, "hostPort": 4002, "protocol": "tcp"}], "essential": true, "environment": [{"name": "AWS_REGION", "value": "eu-central-1"}], "secrets": [{"valueFrom": "arn:aws:secretsmanager:eu-central-1:101924221316:secret:noxy/decryption-key-vq2kuq", "name": "SYMFONY_DECRYPTION_SECRET"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/noxy", "awslogs-region": "eu-central-1", "awslogs-stream-prefix": "ecs"}}, "ulimits": [{"name": "nofile", "softLimit": 32768, "hardLimit": 65536}]}], "family": "noxy", "taskRoleArn": "arn:aws:iam::101924221316:role/ecs-api-noxy", "executionRoleArn": "arn:aws:iam::101924221316:role/ecs-execution-noxy", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "tags": [{"key": "Terraform", "value": "true"}, {"key": "name", "value": "noxy"}]}