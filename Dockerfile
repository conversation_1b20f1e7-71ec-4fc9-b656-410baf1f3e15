ARG BASE_IMAGE=101924221316.dkr.ecr.eu-central-1.amazonaws.com/vt-symfony:latest
FROM ${BASE_IMAGE}
# ? --build-arg BUILD_ENV=dev|prod
ARG BUILD_ENV=prod
ENV ENV=$BUILD_ENV
ENV APP_HOME /var/www/html
ENV USERNAME=www-data
ENV APP_ENV=$BUILD_ENV
ENV APP_RUNTIME_ENV=$BUILD_ENV
WORKDIR $APP_HOME
RUN php -v

# Install dependencies and attempt to install ev extension
RUN apk add --no-cache \
        libev-dev \
        autoconf \
        gcc \
        g++ \
        make \
        re2c \
        libtool \
        php82-dev \
    && pecl list \
    && pecl install ev || echo "ev extension not available, skipping" \
    && if [ $? -eq 0 ]; then docker-php-ext-enable ev; fi \
    && apk del gcc g++ make autoconf re2c libtool php82-dev

RUN apk add --no-cache tini supervisor && rm -rf /tmp/* /var/cache/apk/*

# ! Essentials
RUN echo "Europe/Zagreb" > /etc/timezone
# ! copy config
RUN if [ "$BUILD_ENV" = "dev" ]; then \
      mv "$PHP_INI_DIR/php.ini-development" "$PHP_INI_DIR/php.ini"; \
    else \
      mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"; \
    fi
COPY docker/app.ini $PHP_INI_DIR/conf.d/
COPY docker/app.$BUILD_ENV.ini $PHP_INI_DIR/conf.d/
COPY docker/opcache.$BUILD_ENV.ini $PHP_INI_DIR/conf.d/opcache.ini
COPY docker/php-fpm-docker.conf /usr/local/etc/php-fpm.d/zz-docker.conf
RUN mkdir -p /var/run/php
COPY docker/supervisord.conf /etc/supervisord.conf
COPY docker/worker.conf /etc/supervisor/conf.d/worker.conf
COPY docker/default.conf /etc/nginx/conf.d/
COPY docker/nginx.conf /etc/nginx/
COPY docker/openssl.cnf /etc/ssl/openssl.cnf
COPY docker/docker-entrypoint.sh /usr/local/bin/docker-entrypoint
RUN chmod +x /usr/local/bin/docker-entrypoint
COPY .env /var/www/html/.env
RUN mkdir -p /var/log/supervisor
COPY . ./
RUN rm -Rf docker/
RUN set -eux; \
  if [ "$BUILD_ENV" = "dev" ]; then \
      composer install --no-scripts --no-interaction --no-progress; \
    else \
      composer install --prefer-dist --no-dev --no-scripts --no-progress --no-autoloader; \
    fi
RUN composer clear-cache;
RUN set -eux; \
mkdir -p var/cache var/log; \
    chmod +x bin/console;
#ENTRYPOINT ["docker-entrypoint"] \
ENTRYPOINT ["/sbin/tini", "--", "/usr/local/bin/docker-entrypoint"]
#CMD ["/usr/local/bin/docker-entrypoint"]
