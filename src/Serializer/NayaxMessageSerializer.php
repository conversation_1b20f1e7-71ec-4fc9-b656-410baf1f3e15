<?php

namespace App\Serializer;

use <PERSON><PERSON>fony\Component\Messenger\Envelope;
use <PERSON><PERSON>fony\Component\Messenger\Transport\Serialization\SerializerInterface;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;
use App\Message\NayaxMessage;

class NayaxMessageSerializer implements SerializerInterface
{
    private Serializer $serializer;

    public function __construct()
    {
        $this->serializer = new Serializer([new ObjectNormalizer()], [new JsonEncoder()]);
    }

    public function decode(array $encodedEnvelope): Envelope
    {

      if (!isset($encodedEnvelope['body']) || !isset($encodedEnvelope['headers'])) {
          throw new \LogicException('Encoded envelope must have a "body" and "headers".');
      }

      $data = $this->serializer->decode($encodedEnvelope['body'], 'json');

      $nayaxMessage = new NayaxMessage(
          $data['machineId'] ?? "",
          $data['payedValue'] ?? 0,
          ['epochSecond' => $data['createdDate']['epochSecond'] ?? "", 'nano' => $data['createdDate']['nano'] ?? ""]
      );

      return new Envelope($nayaxMessage);
    }

    public function encode(Envelope $envelope): array
    {
        // Retrieve the message from the envelope
        $nayaxMessage = $envelope->getMessage();

        // Encode the message to JSON format
        $body = $this->serializer->encode([
            'machineId' => $nayaxMessage->getMachineId(),
            'payedValue' => $nayaxMessage->getPayedValue(),
            'createdDate' => [
                'epochSecond' => $nayaxMessage->getCreatedDate()['epochSecond'],
                'nano' => $nayaxMessage->getCreatedDate()['nano'],
            ]
        ], 'json');

        return [
            'body' => $body, // JSON encoded message body
            'headers' => [
                'type' => get_class($nayaxMessage), // Add the message class as the type header
            ],
        ];
    }
}
