<?php

namespace App\Repository;

use App\Entity\GVend;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<GVend>
 */
class GVendRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, GVend::class);
    }

    /**
     * Find unprocessed GVend records (proc = 0)
     * Uses the existing index on 'proc' column for efficient querying
     * Excludes failed records (proc = -1) which are handled manually
     * 
     * @param int $limit Maximum number of records to return
     * @return GVend[]
     */
    public function findUnprocessed(int $limit = 100): array
    {
        return $this->createQueryBuilder('g')
            ->andWhere('g.proc = :proc')
            ->setParameter('proc', 0)
            ->orderBy('g.created', 'ASC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find records by processing status
     * 
     * @param int $proc Processing status (0 = unprocessed, 1 = processed, etc.)
     * @param int $limit Maximum number of records to return
     * @return GVend[]
     */
    public function findByProcessingStatus(int $proc, int $limit = 100): array
    {
        return $this->createQueryBuilder('g')
            ->andWhere('g.proc = :proc')
            ->setParameter('proc', $proc)
            ->orderBy('g.created', 'ASC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Count unprocessed records
     * 
     * @return int
     */
    public function countUnprocessed(): int
    {
        return $this->createQueryBuilder('g')
            ->select('COUNT(g.id)')
            ->andWhere('g.proc = :proc')
            ->setParameter('proc', 0)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Mark a record as processed
     * 
     * @param GVend $gVend
     * @param int $procStatus New processing status (default: 1 = processed)
     * @return void
     */
    public function markAsProcessed(GVend $gVend, int $procStatus = 1): void
    {
        $gVend->setProc($procStatus);
        $this->getEntityManager()->persist($gVend);
        $this->getEntityManager()->flush();
    }

    /**
     * Batch update processing status for multiple records
     * 
     * @param array $ids Array of GVend IDs
     * @param int $procStatus New processing status
     * @return int Number of affected rows
     */
    public function batchUpdateProcessingStatus(array $ids, int $procStatus): int
    {
        if (empty($ids)) {
            return 0;
        }

        return $this->createQueryBuilder('g')
            ->update()
            ->set('g.proc', ':procStatus')
            ->where('g.id IN (:ids)')
            ->setParameter('procStatus', $procStatus)
            ->setParameter('ids', $ids)
            ->getQuery()
            ->execute();
    }

    /**
     * Find records created within a specific time range
     * 
     * @param \DateTimeInterface $from
     * @param \DateTimeInterface $to
     * @param int|null $proc Optional processing status filter
     * @return GVend[]
     */
    public function findByCreatedRange(\DateTimeInterface $from, \DateTimeInterface $to, ?int $proc = null): array
    {
        $qb = $this->createQueryBuilder('g')
            ->andWhere('g.created BETWEEN :from AND :to')
            ->setParameter('from', $from)
            ->setParameter('to', $to)
            ->orderBy('g.created', 'ASC');

        if ($proc !== null) {
            $qb->andWhere('g.proc = :proc')
               ->setParameter('proc', $proc);
        }

        return $qb->getQuery()->getResult();
    }
}
