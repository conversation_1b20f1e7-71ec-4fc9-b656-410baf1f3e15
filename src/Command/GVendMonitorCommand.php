<?php

namespace App\Command;

use App\Repository\GVendRepository;
use App\Entity\GVend;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Doctrine\ORM\EntityManagerInterface;
use App\Service\DeviceService;
use App\Service\DepositService;
use App\Service\CompanyService;
use App\Service\PaymentService;
use App\Service\DeviceTagService;
use App\Service\LocationService;
use App\Service\DeviceConfigurationService;

#[AsCommand(name: 'app:gvend-monitor')]
class GVendMonitorCommand extends Command
{
    protected static $defaultName = 'app:gvend-monitor';

    public function __construct(
        private GVendRepository $gVendRepository,
        private EntityManagerInterface $entityManager,
        private LoggerInterface $logger,
        private ParameterBagInterface $params,
        private DeviceService $deviceService,
        private DepositService $depositService,
        private CompanyService $companyService,
        private PaymentService $paymentService,
        private DeviceTagService $deviceTagService,
        private LocationService $locationService,
        private DeviceConfigurationService $deviceConfigurationService,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Monitor GVend table for new records and process them')
            ->addOption(
                'interval',
                'i',
                InputOption::VALUE_OPTIONAL,
                'Monitoring interval in seconds',
                null
            )
            ->addOption(
                'batch-size',
                'b',
                InputOption::VALUE_OPTIONAL,
                'Number of records to process in each batch',
                null
            )
            ->addOption(
                'single-run',
                's',
                InputOption::VALUE_NONE,
                'Run once and exit (for testing)'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        // Get configuration parameters
        $interval = $input->getOption('interval') ?? $this->params->get('gvend.monitor.default_interval');
        $batchSize = $input->getOption('batch-size') ?? $this->params->get('gvend.monitor.default_batch_size');
        $singleRun = $input->getOption('single-run');

        $io->title('GVend Monitor');
        $io->info(sprintf('Starting GVend Monitor with interval: %ds, batch size: %d, single run: %s',
            $interval, $batchSize, $singleRun ? 'yes' : 'no'));

        $this->logger->warning('GVend Monitor started', [
            'interval' => $interval,
            'batch_size' => $batchSize,
            'single_run' => $singleRun
        ]);

        $iteration = 0;

        do {
            $iteration++;
            $startTime = microtime(true);

            try {
                $io->section(sprintf('Iteration #%d', $iteration));

                // Check database connection
                $io->text('Checking database connection...');
                $this->checkDatabaseConnection();
                $io->success('Database connection OK');

                // Process unprocessed records
                $io->text(sprintf('Processing unprocessed records (batch size: %d)...', $batchSize));
                $processedCount = $this->processUnprocessedRecords($batchSize, $io);

                $endTime = microtime(true);
                $processingTime = round(($endTime - $startTime) * 1000, 2);

                if ($processedCount > 0) {
                    $io->success(sprintf('Processed %d records in %s ms', $processedCount, $processingTime));
                } else {
                    $io->text('No records to process');
                }

                $this->logger->warning('GVend records processed', [
                    'iteration' => $iteration,
                    'processed_count' => $processedCount,
                    'processing_time_ms' => $processingTime
                ]);

                if (!$singleRun) {
                    $io->text(sprintf('Waiting %d seconds before next iteration...', $interval));
                    sleep($interval);
                }

            } catch (\Exception $e) {
                $io->error(sprintf('Error in iteration #%d: %s', $iteration, $e->getMessage()));

                $this->logger->warning('GVend Monitor error', [
                    'iteration' => $iteration,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                if (!$singleRun) {
                    $io->text(sprintf('Waiting %d seconds before retry...', $interval));
                    sleep($interval);
                }
            }

        } while (!$singleRun);

        $io->success('GVend Monitor stopped');
        $this->logger->warning('GVend Monitor stopped');

        return Command::SUCCESS;
    }

    private function processUnprocessedRecords(int $batchSize, SymfonyStyle $io): int
    {
        $unprocessedRecords = $this->gVendRepository->findUnprocessed($batchSize);

        if (empty($unprocessedRecords)) {
            return 0;
        }

        $io->text(sprintf('Found %d unprocessed records', count($unprocessedRecords)));
        $processedCount = 0;
        $failedCount = 0;


        foreach ($unprocessedRecords as $record) {
            // Delete test records without processing
            if ($record->isTest()) {
                $io->text(sprintf('Deleting test record ID: %d (IMEI: %s)', $record->getId(), $record->getImei()));
                $this->entityManager->beginTransaction();
                try {
                    $this->entityManager->remove($record);
                    $this->entityManager->flush();
                    $this->entityManager->commit();
                    $io->text('✓ Test record deleted');
                    $this->logger->warning('Deleted test record', [
                        'id' => $record->getId(),
                        'imei' => $record->getImei()
                    ]);
                } catch (\Exception $e) {
                    $this->entityManager->rollback();
                    $this->markRecordAsFailed($record, $e);
                    $io->error(sprintf('Failed to delete test record: %s', $e->getMessage()));
                    $this->logger->warning('Failed to delete test record', [
                        'id' => $record->getId(),
                        'error' => $e->getMessage()
                    ]);
                }
                continue;
            }

            // Process each record in its own transaction
            $io->text(sprintf('Processing record ID: %d (IMEI: %s, Type: %s, Amount: %s)',
                $record->getId(), $record->getImei(), $record->getPaymentType(), $record->getAmount()));

            $this->entityManager->beginTransaction();

            try {
                $this->processRecord($record, $io);

                // Delete the record after successful processing
                $this->entityManager->remove($record);
                $this->entityManager->flush();
                $this->entityManager->commit();

                $processedCount++;
                $io->text('✓ Record processed and deleted successfully');

                $this->logger->warning('Successfully processed and deleted GVend record', [
                    'id' => $record->getId(),
                    'imei' => $record->getImei(),
                    'payment_type' => $record->getPaymentType()
                ]);

            } catch (\Exception $e) {
                $this->entityManager->rollback();
                $failedCount++;

                // Mark as failed (status -1)
                $this->markRecordAsFailed($record, $e);
                $io->error(sprintf('✗ Failed to process record: %s', $e->getMessage()));

                $this->logger->warning('Failed to process GVend record', [
                    'record_id' => $record->getId(),
                    'imei' => $record->getImei(),
                    'payment_type' => $record->getPaymentType(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        if ($failedCount > 0) {
            $io->warning(sprintf('Batch completed: %d processed, %d failed', $processedCount, $failedCount));
        } else if ($processedCount > 0) {
            $io->success(sprintf('Batch completed: %d records processed successfully', $processedCount));
        }

        return $processedCount;
    }

    private function processRecord(GVend $record, SymfonyStyle $io): void
    {
        $this->logger->warning('Processing GVend record', [
            'id' => $record->getId(),
            'imei' => $record->getImei(),
            'amount' => $record->getAmount(),
            'payment_type' => $record->getPaymentType(),
            'type' => $record->getType(),
            'created' => $record->getCreated()->format('Y-m-d H:i:s'),
            'proc' => $record->getProc()
        ]);

        $io->text('  → Looking up device...');
        $device = $this->deviceService->getByLoggerImei($record->getImei());
        if (!$device) {
            throw new \Exception("Device not found for IMEI: " . $record->getImei());
        }
        $io->text(sprintf('  → Device found: ID %d', $device["id"]));

        $io->text('  → Looking up company...');
        $company = $this->companyService->getById($device["company_id"]);
        if (!$company) {
            throw new \Exception("Company not found for company_id: " . $device["company_id"]);
        }
        $io->text(sprintf('  → Company found: %s', $company["name"] ?? 'ID ' . $company["id"]));

        $io->text('  → Getting device configuration and related data...');
        $deviceConfiguration = $this->deviceConfigurationService->getByImei($record->getImei());
        $deviceEventMeta = json_decode($device["event_meta"], true) ?? [];
        $deviceTags = $this->deviceTagService->getTagsByDeviceId($device["id"]);
        $location = $this->locationService->getById($device["location_id"]);

        $valuesToBeUpdated = [];
        if ($device["is_problematic"]) {
            $valuesToBeUpdated["is_problematic"] = false;
        }

        $io->text(sprintf('  → Processing payment type: %s', $record->getPaymentType()));
        switch($record->getPaymentType()) {
            case 'ping':
                $io->text('  → Handling ping event');
                $deviceEventMeta['lastPing'] = $record->getCreated()->format('Y-m-d H:i:s');
                $valuesToBeUpdated["is_critical"] = false;
                break;
            case 'empty':
                $io->text('  → Handling empty event');
                $deviceEventMeta['lastEmpty'] = $record->getCreated()->format('Y-m-d H:i:s');
                if ($device["saldo"] != 0 or ($device["token"] != 0 and !$device["is_monster_device"])) {
                    $io->text(sprintf('  → Creating deposit: saldo=%s, token=%s', $device["saldo"], $device["token"]));
                    $this->depositService->create($device["saldo"], $device["id"], $device["is_monster_device"] ? null : $device["token"]);
                }
                if (!$device["is_monster_device"]) {
                    $valuesToBeUpdated["token"] = 0;
                }
                $valuesToBeUpdated["saldo"] = 0;
                $this->deviceService->processEmpty($record->getImei(), $company);
                break;
            case 'pay_token':
                $io->text('  → Handling pay_token event');
                $deviceEventMeta['lastPayToken'] = $record->getCreated()->format('Y-m-d H:i:s');
                $deviceEventMeta['lastPay'] = $record->getCreated()->format('Y-m-d H:i:s');
                $deviceEventMeta['lastPayType'] = 'pay_token';
                $this->paymentService->processGVendPayToken($record, $device, $deviceConfiguration, $company, $deviceTags, $location);
                $valuesToBeUpdated["is_critical"] = false;
                break;
            case 'pay_coin':
                $io->text('  → Handling pay_coin event');
                $deviceEventMeta['lastPayCoin'] = $record->getCreated()->format('Y-m-d H:i:s');
                $deviceEventMeta['lastPay'] = $record->getCreated()->format('Y-m-d H:i:s');
                $deviceEventMeta['lastPayType'] = 'pay_coin';
                $this->paymentService->processGVendPayCoin($record, $device, $deviceConfiguration, $company, $deviceTags, $location);
                $valuesToBeUpdated["is_critical"] = false;
                break;
            case 'token_drop':
                $io->text('  → Handling token_drop event');
                $deviceEventMeta['lastTokenDrop'] = $record->getCreated()->format('Y-m-d H:i:s');
                $this->paymentService->processGVendTokenDrop($record, $device, $deviceConfiguration, $company, $deviceTags, $location);
                $valuesToBeUpdated["is_critical"] = false;
                break;
            case 'pay_rfcard':
                $io->text('  → Handling pay_rfcard event (loyalty card)');
                $deviceEventMeta['lastPayRfCard'] = $record->getCreated()->format('Y-m-d H:i:s');
                $deviceEventMeta['lastPay'] = $record->getCreated()->format('Y-m-d H:i:s');
                $deviceEventMeta['lastPayType'] = 'pay_rfcard';
                $this->paymentService->processGVendPayRfCard($record, $device, $deviceConfiguration, $company, $deviceTags, $location);
                $valuesToBeUpdated["is_critical"] = false;
                break;
            default:
                throw new \Exception("Unknown payment type: " . $record->getPaymentType());
        }

        $io->text('  → Updating device metadata...');
        $device['event_meta'] = json_encode($deviceEventMeta);
        $valuesToBeUpdated["event_meta"] = $device['event_meta'];
        $valuesToBeUpdated["updated_at"] = date('Y-m-d H:i:s');

        $this->deviceService->updateDevice($valuesToBeUpdated, $device["id"]);
        $io->text('  → Device updated successfully');
    }

    private function markRecordAsFailed(GVend $record, \Exception $e): void
    {
        try {
            $this->entityManager->beginTransaction();
            $record->setProc(-1); // Mark as failed
            $this->entityManager->persist($record);
            $this->entityManager->flush();
            $this->entityManager->commit();
        } catch (\Exception $markingException) {
            $this->entityManager->rollback();
            $this->logger->warning('Could not mark record as failed', [
                'record_id' => $record->getId(),
                'original_error' => $e->getMessage(),
                'marking_error' => $markingException->getMessage()
            ]);
        }
    }

    private function checkDatabaseConnection(): void
    {
        try {
            // Test database connection
            $this->entityManager->getConnection()->executeQuery('SELECT 1');
        } catch (\Exception $e) {
            $this->logger->warning('Database connection lost, attempting to reconnect');
            $this->entityManager->getConnection()->close();
            $this->entityManager->getConnection()->connect();
            throw new \Exception('Database connection failed: ' . $e->getMessage());
        }
    }
}
