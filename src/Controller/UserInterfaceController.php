<?php

namespace App\Controller;

use App\Enum\SocketCommunication;
use App\Service\DeviceConfigurationService;
use App\Service\DeviceService;
use App\Service\EncodeSettingsService;
use App\Util\SocketUtil;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\Routing\Attribute\Route;

class UserInterfaceController extends AbstractController
{
    public function __construct(
        private Security $security,
        private LoggerInterface $logger,
        private DeviceService $deviceService,
        private EncodeSettingsService $ess,
        private DeviceConfigurationService $deviceConfigurationService,
        private SocketUtil $socketUtil
        )
    {

    }

    #[Route(path: '/', name: 'app_user_interface')]
    public function index(Request $request): Response
    {
        $imei = $request->query->get("imei");

        if($imei){
            try{
                $loggerData = $this->socketUtil->requestData($imei, SocketCommunication::RequestImeiStatus->value);

                if($loggerData){
                    $device = $this->deviceService->getByLoggerImei($imei);

                    return $this->render('user_interface/index.html.twig', [
                        ...json_decode($loggerData, true),
                        ...$device,
                        'imei' => $imei
                    ]);
                }
                else{
                    return $this->render('user_interface/index.html.twig', [
                        'error' => "Device with that IMEI is not connected.",
                        'imei' => $imei
                    ]);
                }
            }catch(\Exception $e){
                $this->logger->warning($e->getMessage());
            }
        }

        return $this->render('user_interface/index.html.twig');
    }


    #[Route(path: "/start-game", name: "manual_start_game", methods: ["POST"])]
    public function startGame(Request $request): JsonResponse {

        $user = $this->getUser();

        if(!$user){
            throw new UnauthorizedHttpException("Unauthorized", "Unauthorized");
        }

        $imei = $request->request->get("imei");
        $code = $request->request->get("code");

        if($imei){
            try{
                $this->socketUtil->requestData($imei, SocketCommunication::DirectWrite->value, $this->ess->encodeSettingsElement("01", "S", "byte", 1) . ">>>");
                return $this->json(["message" => "Success"]);
            }catch(\Exception $e){
                return $this->json(["error" => "Manual start game via imei error: " . $e->getMessage()]);
            }
        }

        if($code){
            $deviceConfiguration = $this->deviceConfigurationService->getByStartGameCode($code);

            if($deviceConfiguration and isset(json_decode($deviceConfiguration["configuration"], true)["start-game"]["code"])){
                $imei = $deviceConfiguration["imei"];
            }

            if($imei and !empty($imei)){
                try{
                    $this->socketUtil->requestData($imei, SocketCommunication::DirectWrite->value, $this->ess->encodeSettingsElement("01", "S", "byte", 1) . ">>>");
                    return $this->json(["message" => "Success"]);
                }catch(\Exception $e){
                    return $this->json(["error" => "Manual start game via code error: " . $e->getMessage()]);
                }
            }
        }

        return new JsonResponse(["error" => "Code or IMEI are invalid!"]);
    }

    #[Route(path: "/reload-firmware", name: "reload_firmware", methods: ["POST"])]
    public function reloadFirmware(){
        $user = $this->getUser();

        if(!$user){
            throw new UnauthorizedHttpException("Unauthorized", "Unauthorized");
        }

        try{
            $this->socketUtil->sendData(SocketCommunication::ReloadFirmware->value);
            return $this->json(["message" => "Success"]);
        }catch(\Exception $e){
            return $this->json(["error" => "Reload firmware error: " . $e->getMessage()]);
        }

    }

    #[Route(path: "/update-device", name: "update_device", methods: ["POST"])]
    public function updateDeviceFirmware(Request $request){
        $user = $this->getUser();

        if(!$user){
            throw new UnauthorizedHttpException("Unauthorized", "Unauthorized");
        }

        $imei = $request->request->get("imei");

        if(!$imei){
            throw new BadRequestException("IMEI is required");
        }

        try{
            $this->socketUtil->sendData(SocketCommunication::UpdateDeviceFirmware->value, $imei);
            return $this->json(["message" => "Success"]);
        }catch(\Exception $e){
            return $this->json(["error" => "Firmware update error: " . $e->getMessage()]);
        }

        return new JsonResponse(["error" => "Something went wrong!"]);
    }

}
