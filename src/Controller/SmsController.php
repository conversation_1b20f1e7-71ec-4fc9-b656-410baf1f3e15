<?php

namespace App\Controller;

use App\Enum\SocketCommunication;
use App\Service\CompanyService;
use App\Service\DeviceConfigurationService;
use App\Service\DeviceService;
use App\Service\EncodeSettingsService;
use App\Service\FirmwareService;
use App\Service\LocationService;
use App\Service\PaymentService;
use App\Service\SocketManagementService;
use App\Service\TransactionService;
use App\Util\SocketUtil;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\DBAL\Connection;

class SmsController extends AbstractController
{

  public function __construct(
    private SocketManagementService $socketManager,
    private DeviceConfigurationService $deviceConfigurationService,
    private EncodeSettingsService $ess,
    private FirmwareService $firmwareService,
    private LoggerInterface $logger,
    private SocketUtil $socketUtil,
    private PaymentService $paymentService,
    private LocationService $locationService,
    private DeviceService $deviceService,
    private CompanyService $companyService,
    private Connection $connection,
    private TransactionService $transactionService
  ) {}

  #[Route(path: "/sms", name: "sms", methods: ["POST"])]
  public function sms(Request $request)
  {
    $body = json_decode($request->getContent());

    if (!$body) {
      return $this->json(["code" => 1]);
    }

    $code = $body->keyword;
    $imei = "";

    $deviceConfiguration = $this->deviceConfigurationService->getByStartGameCode($code);

    if ($deviceConfiguration and isset(json_decode($deviceConfiguration["configuration"], true)["start-game"]["code"])) {
      $imei = $deviceConfiguration["imei"];
    } else if (isset($body->imei)) {
      $imei = $body->imei;
    }

    if (!$imei or empty($imei)) {
      return $this->json(["code" => 1]);
    }

    try {
      $rawResponse = $this->socketUtil->requestData($imei, SocketCommunication::DirectWrite->value, $this->ess->encodeSettingsElement("01", "S", "byte", 1) . ">>>");
      $response = json_decode($rawResponse, true);

      if (isset($response["error"])) {
        sleep(15);
        $retryRawResponse = $this->socketUtil->requestData($imei, SocketCommunication::DirectWrite->value, $this->ess->encodeSettingsElement("01", "S", "byte", 1) . ">>>");
        $retryResponse = json_decode($retryRawResponse, true);

        if (isset($retryResponse["error"])) {
          $this->logger->warning("Socket write failed even after delay.", ["context" => __CLASS__]);
          return $this->json(["code" => 1]);
        }
        return $this->json(["code" => 0]);
      }

      return $this->json(["code" => 0]);
    } catch (\Exception $e) {
      $this->logger->warning("Start SMS error: " . $e->getMessage(), ["context" => __CLASS__]);
      return $this->json(["code" => 1]);
    }
  }

  #[Route(path: "/test-top-gun", name: "test")]
  public function testTopGun()
  {
    $testMAketa = $this->deviceService->getByLoggerImei("867280067212613");

    $location = $this->locationService->getById($testMAketa['location_id']);

    $isFiscal = $this->paymentService->fiscalCheckTestMaketa($testMAketa,   $location, 'pay_coin', true);

    return $this->json(['message' => $isFiscal]);;
  }
}
