<?php

namespace App\Util;

use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class SocketUtil {
  public function __construct(private readonly ParameterBagInterface $params)
  {

  }


  public function sendData($type, $imei = null){
    $tcpSocket = stream_socket_client($this->params->get('noxyTcpEndpoint'),  $errstr, $errno);
    if ($tcpSocket) {
      $message = json_encode(['imei' => $imei ?? null, 'type' => $type]) . "\n";
      fwrite($tcpSocket, $message);
      fclose($tcpSocket);
    }else {
      throw new \Exception("Unable to connect to the socket: $errstr ($errno)");
    }
  }

  public function requestData($imei, $type, $data = null) {
    $tcpSocket = stream_socket_client($this->params->get('noxyTcpEndpoint'), $errno, $errstr, 30);

    if ($tcpSocket) {
        $message = json_encode(['imei' => $imei, 'type' => $type, "data" => $data ?? null]) . "\n";
        fwrite($tcpSocket, $message);

        $responseData = '';
        while (true) {
            $responseData .= fread($tcpSocket, 1);
            if (strpos($responseData, '}') !== false) {
                break;
            }
        }

        fclose($tcpSocket);

        return "{" . explode("{", $responseData)[1];
    } else {
        throw new \Exception("Unable to connect to the socket: $errstr ($errno)");
    }
}
}