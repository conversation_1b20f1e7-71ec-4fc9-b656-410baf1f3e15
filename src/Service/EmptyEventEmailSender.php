<?php

namespace App\Service;

use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;

class EmptyEventEmailSender
{
  public function __construct(private readonly MailerInterface $mailer) {}

  /**
   * @throws TransportExceptionInterface
   */
  public function send(array $emails, array $data): void
  {
    // create email
    $email = (new TemplatedEmail())
      ->from(new Address('<EMAIL>', 'VendingTycoon'))
      ->to(...$emails)
      ->subject(sprintf('New Empty Event: %s', date('d.m.y @ H:i')))
      ->htmlTemplate('email/empty/empty-event.html.twig')
      ->context([
        'dateTime' => date('d.m.y @ H:i'),
        'data' => $data,
      ]);

    // add headers
    $email->getHeaders()
      ->addTextHeader('X-Auto-Response-Suppress', 'OOF, DR, RN, NRN, AutoReply')
      ->addTextHeader('List-Unsubscribe', '<mailto:<EMAIL>?subject=unsubscribe>');

    // send email
    $this->mailer->send($email);
  }
}
