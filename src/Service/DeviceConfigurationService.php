<?php

namespace App\Service;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\DBAL\Connection;

class DeviceConfigurationService {

  private array $cachedConfigurations = [];

  public function __construct(private EntityManagerInterface $em, private Connection $connection, private EncodeSettingsService $ess)
  {

  }

  public function getByImei(string $imei) {
    return $this->connection->fetchAssociative('SELECT * FROM device_configuration WHERE imei = ? ORDER BY updated_at DESC LIMIT 1', [$imei]);
  }

  public function getByStartGameCode(string $code) {
    return $this->connection->fetchAssociative(
        "SELECT * FROM device_configuration
         WHERE JSON_UNQUOTE(JSON_EXTRACT(configuration, '$.\"start-game\".code')) = ?
         ORDER BY updated_at DESC LIMIT 1",
        [$code]
    );
  }

  public function addCachedConfiguration (string $imei, $configuration){
    $this->cachedConfigurations[$imei] = ["configuration" => $configuration, "timestamp" => date('Y-m-d H:i:s')];
  }

  public function getCachedConfiguration (string $imei){
    return $this->cachedConfigurations[$imei] ?? null;
  }



  public function encodeSettings(array $s, bool $start_update, string $imei, bool $request_feedback, int $num_messages): string {
    $count = 0;
    $result = "";

    // Simplified by creating helper functions for repetitive logic
    $result = $this->encodeLowPowerMode($result, $s, $count);
    $result = $this->encodeLineDirections($result, $s, $count);
    $result = $this->encodeRequestFeedback($result, $request_feedback, $num_messages, $count);
    $result = $this->encodeStartGame($result, $s, $count);
    $result = $this->encodePulseSettings($result, $s, $count);
    $result = $this->encodeParallelSettings($result, $s, $count);
    $result = $this->encodeAcceptReject($result, $s, $count);
    $result = $this->encodeCctalkSciMdb($result, $s, $count);
    $result = $this->encodeExecSettings($result, $s, $count);

    // Encode SMS reply number if available
    if (isset($s['start-game']['sms_reply_number'])) {
        $result = $this->ess->encodeSettingsElement($result, 'O', 'string', $s['start-game']['sms_reply_number']);
        $count++;
    }

    // Encode start update flag
    if ($start_update) {
        $result = $this->ess->encodeSettingsElement($result, 'P', 'byte', 1);
        $count++;
    }

    return $this->ess->byteToHex($count) . $result;
}

  // Helper functions to reduce code repetition
  private function encodeLowPowerMode(string $result, array $s, int &$count): string {
      $result = $this->ess->encodeSettingsElement($result, '0', 'byte', isset($s['low_power_mode']) ? $s['low_power_mode'] : 0);
      $count++;
      return $result;
  }

  private function encodeLineDirections(string $result, array $s, int &$count): string {
      if (isset($s['line_directions'])) {
          $result = $this->ess->encodeSettingsElement($result, '1', 'byte_array11', $s['line_directions']);
          $count++;
      }
      return $result;
  }

  private function encodeRequestFeedback(string $result, bool $request_feedback, int $num_messages, int &$count): string {
      if ($request_feedback) {
          $result = $this->ess->encodeSettingsElement($result, 'f', 'byte', $num_messages);
          $count++;
      }
      return $result;
  }

  private function encodeStartGame(string $result, array $s, int &$count): string {
      if (isset($s['start-game'])) {
          // Outputs
          if (isset($s['start-game']['outputs'])) {
            $outputs = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
            for ($i = 0; $i < count($s["start-game"]["outputs"]); $i++){
                if($s["start-game"]["outputs"][$i] && $s["start-game"]["outputs"][$i] <= 10){
                    $outputs[$s["start-game"]["outputs"][$i]] = 1;
                }

            $result = $this->ess->encodeSettingsElement($result, "2", "byte_array11", $outputs);
            $count++;
            }
          }

          // Pulse duration, game duration, and game starts
          $result = $this->ess->encodeSettingsElement($result, '3', 'short', $s['start-game']['start_game_pulse_duration'] ?? 1);
          $result = $this->ess->encodeSettingsElement($result, '4', 'short', $s['start-game']['game_duration'] ?? 1);
          $result = $this->ess->encodeSettingsElement($result, '5', 'short', $s['start-game']['num_game_starts'] ?? 1);
          $count += 3;
      }

      return $result;
  }


  private function encodePulseSettings(string $result, array $s, int &$count): string {
      if (isset($s['pulse'])) {
          $io_pulse_inputs = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
          $io_pulse_duration_min = [30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30];
          $io_pulse_duration_max = [300, 300, 300, 300, 300, 300, 300, 300, 300, 300, 300];

          foreach($s['pulse'] as $line => $pulse){
            if($line < 0 or $line > 11){
                continue;
            }

            $io_pulse_inputs[$line] = 1;
            if(isset($s["pulse"][$line]) && isset($s["pulse"][$line]["group"])){
                $io_pulse_inputs[$line] = 2;
            }
            if(isset($s["pulse"][$line]) && isset($s["pulse"][$line]["duration"]) && count($s["pulse"][$line]["duration"]) == 2){
                $io_pulse_duration_min[$line] = $s["pulse"][$line]["duration"][0];
                $io_pulse_duration_max[$line] = $s["pulse"][$line]["duration"][1];
            }
          }


          $result = $this->ess->encodeSettingsElement($result, '6', 'byte_array11', $io_pulse_inputs);
          $result = $this->ess->encodeSettingsElement($result, '7', 'short_array11', $io_pulse_duration_min);
          $result = $this->ess->encodeSettingsElement($result, '8', 'short_array11', $io_pulse_duration_max);
          $count += 3;
      }

      return $result;
  }

  private function encodeParallelSettings(string $result, array $s, int &$count): string {
        if(isset($s["parallel"]) && isset($s["parallel"]["inputs"]) && count($s["parallel"]["inputs"])){
            $io_parallel_inputs = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
            for ($i = 0; $i < count($s["parallel"]["inputs"]); $i++) {
                if($s["parallel"]["inputs"][$i] < 0 or $s["parallel"]["inputs"][$i] > 11){
                    continue;
                }
                $io_parallel_inputs[$s["parallel"]["inputs"][$i]] = 1;
            }

            $result = $this->ess->encodeSettingsElement($result, '9', 'byte_array11', $io_parallel_inputs);
            $count++;
        }

      // Parallel duration

        if(isset($s["parallel"]) && isset($s["parallel"]["duration"]) && count($s["parallel"]["duration"]) == 2){
            $result = $this->ess->encodeSettingsElement($result, 'A', 'short', $s['parallel']['duration'][0]);
            $result = $this->ess->encodeSettingsElement($result, 'B', 'short', $s['parallel']['duration'][1]);
        } else {
            $result = $this->ess->encodeSettingsElement($result, 'A', 'short', 30);
            $result = $this->ess->encodeSettingsElement($result, 'B', 'short', 300);
        }

        $count += 2;

        return $result;
  }

  private function encodeAcceptReject(string $result, array $s, int &$count): string {
      if (isset($s["accept-reject"]) && $s['accept-reject']['status']) {
        $io_accept_inputs = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        $io_reject_inputs = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];

        if(isset($s["accept-reject"]["accept_inputs"]) && count($s["accept-reject"]["accept_inputs"])){
            for($i = 0; $i < count($s["accept-reject"]["accept_inputs"]); $i++){
                if($s["accept-reject"]["accept_inputs"][$i] < 0 or $s["accept-reject"]["accept_inputs"][$i] > 11){
                    continue;
                }
                $io_accept_inputs[$s["accept-reject"]["accept_inputs"][$i]] = 1;
            }
        }

        if(isset($s["accept-reject"]["reject_inputs"]) && count($s["accept-reject"]["reject_inputs"])){
            for($i = 0; $i < count($s["accept-reject"]["reject_inputs"]); $i++){
                if($s["accept-reject"]["reject_inputs"][$i] < 0 or $s["accept-reject"]["reject_inputs"][$i] > 11){
                    continue;
                }
                $io_reject_inputs[$s["accept-reject"]["reject_inputs"][$i]] = 1;
            }
        }

        $result = $this->ess->encodeSettingsElement($result, 'C', 'byte', 1);
        $result = $this->ess->encodeSettingsElement($result, 'D', 'byte_array11', $io_accept_inputs);
        $result = $this->ess->encodeSettingsElement($result, 'E', 'byte_array11', $io_reject_inputs);
        $count += 3;
      }
      return $result;
  }

  private function encodeCctalkSciMdb(string $result, array $s, int &$count): string {
      $result = $this->ess->encodeSettingsElement($result, 'F', 'byte', isset($s['cctalk']) && $s['cctalk']['status'] ? 1 : 0);
      $result = $this->ess->encodeSettingsElement($result, 'K', 'string_list', isset($s['cctalk']) ? $s['cctalk'] : []);

      $result = $this->ess->encodeSettingsElement($result, 'G', 'byte', isset($s['sci']['status']) && $s['sci']['status'] ? 1 : 0);
      $result = $this->ess->encodeSettingsElement($result, 'H', 'byte', isset($s['sci']) ? $s['sci']['payload_length'] : 0);
      $result = $this->ess->encodeSettingsElement($result, 'L', 'string_list', isset($s['sci']) ? $s['sci'] : []);

      $mdb_value = isset($s['mdb']['status']) ? (isset($s['mdb']['inverted']) ? 2 : 1) : 0;
      $result = $this->ess->encodeSettingsElement($result, 'I', 'byte', $mdb_value);
      $result = $this->ess->encodeSettingsElement($result, 'M', 'string_list', isset($s['mdb']) ? $s['mdb'] : []);

      $count += 7;

      return $result;
  }

  private function encodeExecSettings(string $result, array $s, int &$count): string {
    $result = $this->ess->encodeSettingsElement($result, 'J', 'byte', isset($s['exec']['status']) && $s['exec']['status'] ? 1 : 0);
    $result = $this->ess->encodeSettingsElement($result, 'N', 'string_list', isset($s['exec']) ? $s['exec'] : []);

    $count += 2;

    return $result;
  }
}