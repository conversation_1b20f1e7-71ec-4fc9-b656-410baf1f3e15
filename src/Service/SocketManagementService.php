<?php

namespace App\Service;

use SplQueue;

class SocketManagementService {

  private array $sockets = [];

  private array $loggersToUpdate = [];

  private array $connectedLoggers = [];

  private array $restartedLoggers = [];

  private array $loggerMessageTimestamps = [];
  private int $maxTimestampsPerLogger = 20;
  private array $socketLastMessage = [];

  //SOCKETS
  public function addSocket(string $imei, $socket)
  {
      $this->sockets[$imei] = $socket;
  }

  public function getAllSockets()
  {
      return $this->sockets;
  }

  public function getSocket(string $imei)
  {
      return $this->sockets[$imei] ?? null;
  }

  public function removeSocket(string $imei)
  {
      unset($this->sockets[$imei]);
  }

  //LOGGER UPDATE
  public function addLoggerToUpdate(string $imei, $update)
  {
      $this->loggersToUpdate[$imei] = $update;
  }

  public function getLoggerToUpdate(string $imei)
  {
      return $this->loggersToUpdate[$imei] ?? null;
  }

  public function removeLoggerToUpdate(string $imei)
  {
      unset($this->loggersToUpdate[$imei]);
  }

  // CONNECTION DETAILS

  public function addConnectedLogger(string $imei, $data)
  {
      $this->connectedLoggers[$imei] = $data;
  }

  public function getConnectedLogger(string $imei)
  {
      return $this->connectedLoggers[$imei] ?? null;
  }

  public function removeConnectedLogger(string $imei)
  {
      unset($this->connectedLoggers[$imei]);
  }

  // LOGGER RESET
  public function addRestartedLogger(string $imei, $data)
  {
      $this->restartedLoggers[$imei] = $data;
  }

  public function getRestartedLogger(string $imei)
  {
      return $this->restartedLoggers[$imei] ?? null;
  }

  public function removeAllRestartedLoggers()
  {
        $this->restartedLoggers = [];
  }

  // LOGGER TIMESTAMP (UNIQUE ID)
 public function isDuplicateLoggerMessageTimestamp(string $imei, int $timestamp): bool
    {
        if (!isset($this->loggerMessageTimestamps[$imei])) {
            $this->loggerMessageTimestamps[$imei] = new SplQueue();
        }

        /** @var SplQueue $queue */
        $queue = $this->loggerMessageTimestamps[$imei];

        foreach ($queue as $existingTimestamp) {
            if ($existingTimestamp == $timestamp) {
                return true;
            }
        }

        $queue->enqueue($timestamp);
        if ($queue->count() > $this->maxTimestampsPerLogger) {
            $queue->dequeue();
        }

        return false;
    }

    public function clearLoggerMessageTimestamps(): void
    {
        $this->loggerMessageTimestamps = [];
    }
    
    // LAST MESSAGE
    public function addSocketLastMessage(string $imei, $timestamp)
    {
        $this->socketLastMessage[$imei] = $timestamp;
    }

    public function getSocketLastMessage(string $imei)
    {
        return $this->socketLastMessage[$imei] ?? null;
    }

    public function removeSocketLastMessage(string $imei)
    {
        unset($this->socketLastMessage[$imei]);
    }
}