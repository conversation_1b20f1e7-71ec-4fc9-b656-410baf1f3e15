<?php

namespace App\Service;

use Doctrine\DBAL\Connection;

class CompanyService {
  public function __construct(private Connection $connection)
  {

  }

  // public function getByOib($oib){
  //   return $this->connection->fetchAssociative("SELECT c.*, cc.id as certificate_id, cc.company_id, cc.cert_file, cc.cert_pass, cc.expires_at FROM company c RIGHT JOIN company_certificate cc ON cc.company_id = c.id WHERE c.oib = ?", [$oib]);
  // }

  public function getByOib($oib)
  {
    $pdo = $this->connection->getNativeConnection();

    $sql = "SELECT c.*, cc.id as certificate_id, cc.company_id, cc.cert_file, cc.cert_pass, cc.expires_at
            FROM company c
            LEFT JOIN company_certificate cc ON cc.company_id = c.id
            WHERE c.oib = :oib";

    $stmt = $pdo->prepare($sql);

    $stmt->bindValue(':oib', $oib, \PDO::PARAM_STR);

    $stmt->execute();

    $certFileStream = null;
    $stmt->bindColumn('cert_file', $certFileStream, \PDO::PARAM_LOB);

    $result = $stmt->fetch(\PDO::FETCH_ASSOC);

    $result['cert_file'] = $certFileStream;

    return $result;
  }

  public function getById($id)
  {
    $pdo = $this->connection->getNativeConnection();

    $sql = "SELECT c.*, cc.id as certificate_id, cc.company_id, cc.cert_file, cc.cert_pass, cc.expires_at
            FROM company c
            LEFT JOIN company_certificate cc ON cc.company_id = c.id
            WHERE c.id = :id";

    $stmt = $pdo->prepare($sql);

    $stmt->bindValue(':id', $id, \PDO::PARAM_STR);

    $stmt->execute();

    $certFileStream = null;
    $stmt->bindColumn('cert_file', $certFileStream, \PDO::PARAM_LOB);

    $result = $stmt->fetch(\PDO::FETCH_ASSOC);

    $result['cert_file'] = $certFileStream;

    return $result;
  }


  // public function getById($id){
  //   // return $this->connection->fetchAssociative("SELECT * FROM company c WHERE id = ?", [$id]);
  //   // return $this->connection->fetchAssociative("SELECT c.*, cc.id as certificate_id, cc.company_id, cc.cert_file, cc.cert_pass, cc.expires_at FROM company c RIGHT JOIN company_certificate cc ON cc.company_id = c.id WHERE c.id = ?", [$id]);

  //   $sql = "SELECT c.*, cc.id as certificate_id, cc.company_id, cc.cert_file, cc.cert_pass, cc.expires_at
  //       FROM company c
  //       RIGHT JOIN company_certificate cc ON cc.company_id = c.id
  //       WHERE c.id = :id";

  //   $stmt = $this->connection->prepare($sql);

  //   $stmt->bindValue(':id', $id, \PDO::PARAM_STR);

  //   $resultSet = $stmt->executeQuery();

  //   $stmt->bindColumn('cert_file', $certFileStream, \PDO::PARAM_LOB);

  //   $result = $resultSet->fetchAssociative();

  //   echo gettype($result["cert_file"]);

  //   return $result;
  // }
}