<?php

namespace App\Service;

use Exception;

class DecryptionService {

    public function revertFilter($data, ?array $config) {
        if($config === null){
            return $data;
        }
        if(isset($config['mdb']) && $config['mdb']['status']){
            $r = $this->revertExactFilter($data, $config['mdb']);
            if($r)
                return $r;
        }

        if(isset($config['exec']) && $config['exec']['status']){
            $r = $this->revertExactFilter($data, $config['exec']);
            if($r)
                return $r;
        }

        if(isset($config['sci']) && $config['sci']['status']){
            $r = $this->revertExactFilter($data, $config['sci']);
            if($r)
                return $r;
        }

        if(isset($config['cctalk']) && $config['cctalk']['status']){
            $r = $this->revertExactFilter($data, $config['cctalk']);
            if($r)
                return $r;
        }

        return implode($data);
    }

    private function revertExactFilter($data, $config){
        if(isset($config['input_values'])){
            foreach($config['input_values'] as $filter => $value){
                if($this->matchFilter($data, str_split($filter))){
                    return $filter;
                }
            }
        }

        if(isset($config['output_values'])){
            foreach($config['output_values'] as $filter => $value){
                if($this->matchFilter($data, str_split($filter))){
                    return $filter;
                }
            }
        }

        if(isset($config['token_values'])){
            foreach($config['token_values'] as $filter => $value){
                if($this->matchFilter($data, str_split($filter))){
                    return $filter;
                }
            }
        }

        return null;
    }

    private function matchFilter($data, $filter) {
        for ($i = 0; $i < count($data); $i++) {
            if ($i >= count($filter)) {
                return false;
            }
            if ($data[$i] == $filter[$i]) {
                continue;
            }
            if ($filter[$i] == '?') {
                continue;
            }
            if ($filter[$i] == '*') {
                return true;
            }
            return false;
        }
        if (count($filter) > count($data)) {
            return false;
        }
        return true;
    }

  public function xxteaDecrypt($encoded, $salt): ?string
    {
        // Use external functions defined at the class level
        $data = $this->convertToUint32Array($encoded);
        $saltArray = $this->convertSaltToArray($salt);
        $key = $this->prepareKey($saltArray);
        $decoded = $this->decryptUint32Array($data, $key);

        // Convert the decoded array back to a binary string
        $result = '';
        foreach ($decoded as $value) {
            $result .= chr($value & 0x7F);
            $result .= chr(($value >> 8) & 0x7F);
            $result .= chr(($value >> 16) & 0x7F);
            $result .= chr(($value >> 24) & 0x7F);
        }

        return $result;
    }

    private function convertToUint32Array($encoded): array
    {
        $data = [];
        $length = strlen($encoded);
        for ($i = 0; $i < $length / 4; $i++) {
            $offset = $i * 4;
            $data[$i] = ord($encoded[$offset]) | (ord($encoded[$offset + 1]) << 8) | (ord($encoded[$offset + 2]) << 16) | (ord($encoded[$offset + 3]) << 24);
        }
        return $data;
    }

    private function convertSaltToArray(string $salt): array
    {
        if (strlen($salt) !== 16) {
            throw new Exception('Salt must be exactly 16 bytes long.');
        }
        $saltArray = [];
        for ($i = 0; $i < 16; $i++) {
            $saltArray[$i] = ord($salt[$i]);
        }
        return $saltArray;
    }

    private function prepareKey(array $saltArray): array
    {
        $key = [0x8441888, 43112609, 0x0f1f3fbe, 17183];
        for ($i = 0; $i < 16; $i++) {
            $key[intdiv($i, 4)] += ($saltArray[$i] << $i);
        }
        return $key;
    }

    private function decryptUint32Array(array $v, array $k): array
    {
        $DELTA = 0x0E3779B9;
        $length = count($v);
        $n = $length - 1;
        $y = $v[0];
        $q = floor(7 + 52 / $length);
        for ($sum = $this->int32($q * $DELTA); $sum !== 0; $sum = $this->int32($sum - $DELTA)) {
            $e = $this->unsignedRightShift($sum, 2) & 3;
            for ($p = $n; $p > 0; --$p) {
                $z = $v[$p - 1];
                $y = $v[$p] = $this->int32($v[$p] - $this->mx($sum, $y, $z, $p, $e, $k));
            }
            $z = $v[$n];
            $y = $v[0] = $this->int32($v[0] - $this->mx($sum, $y, $z, 0, $e, $k));
        }
        return $v;
    }

    private function int32($i) {
        return $i & 0xFFFFFFFF;
    }

    private function unsignedRightShift($value, $shift) {
        return ($value >> $shift) & 0xFFFFFFFF;
    }

    private function mx($sum, $y, $z, $p, $e, $k) {
        return (($this->unsignedRightShift($z, 5) ^ ($y << 2)) + ($this->unsignedRightShift($y, 3) ^ ($z << 4))) ^ (($sum ^ $y) + ($k[$p & 3 ^ $e] ^ $z));
    }

}