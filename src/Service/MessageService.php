<?php

namespace App\Service;

use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Ramsey\Uuid\Uuid;

class MessageService {

  public function __construct(private EntityManagerInterface $em, private Connection $connection)
  {

  }

  public function create(array $data, string $imei): array {
    $uuid = Uuid::uuid4();
    $createdAt = date('Y-m-d H:i:s');

    try{
      $this->connection->insert("message", ["id" => $uuid, "data" => json_encode($data), "imei" => $imei, "created_at" => $createdAt]);
    }catch(\Exception $err){
      // TODO Add CloudWatch Logger!
      echo $err->getMessage();
    }

    return ["id" => $uuid, "data" => $data, "imei" => $imei, "created_at" => $createdAt];
  }

}