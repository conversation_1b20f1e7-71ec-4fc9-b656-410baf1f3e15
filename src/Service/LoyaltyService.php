<?php

namespace App\Service;

use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class LoyaltyService
{
    public function __construct(private readonly HttpClientInterface $client, private readonly ParameterBagInterface $params)
    {
    }

    public function getCard($serialNumber)
    {
        $response = $this->client->request(
            'GET',
            $this->params->get('loyaltyApiEndpoint') . 'api/v1/cards?serialNumber=' . $serialNumber
        );
        $content = $response->toArray();

        if($content["hydra:totalItems"] == 0){
            return null;
        }

        return $content['hydra:member'][0];
    }

    public function updateSaldo($id, $saldo, $cost, $totalSpent, $bonus, $saldoReal)
    {
        $bonusLeft = $bonus;
        $newTotalSpent = $totalSpent;
        $newSaldoReal = $saldoReal;

        try {
            if ($bonus > 0) {

                $bonusLeft = $bonus - $cost;
                if ($bonusLeft < 0) {
                    //Since bonusLeft is a negative number, in order to increase newTotalSpent for absoulute value of the bonusLeft,
                    // we need to substract it from newTotalSpent (e.g.newTotalSpent = 5 - (-1) = 6)
                    //The similar logic is applied to newSaldoReal
                    $newTotalSpent = $newTotalSpent - $bonusLeft;
                    $newSaldoReal = $newSaldoReal + $bonusLeft;
                    $bonusLeft = 0;
                }
            } else {
                $newTotalSpent = $newTotalSpent + $cost;
                $newSaldoReal = $newSaldoReal - $cost;
            }

            $this->client->request(
                'PATCH',
                $this->params->get('loyaltyApiEndpoint') . 'api/v1/update-card-saldo/' . $id,
                [
                    'headers' => ['Content-Type' => 'application/merge-patch+json'],
                    'json' => [
                        'saldo' => $saldo - $cost,
                        'totalSpent' => $newTotalSpent,
                        'saldoBonus' => $bonusLeft,
                        'saldoReal' => $newSaldoReal
                    ],
                ]
            );
        } catch (TransportExceptionInterface $transportException) {
            return $transportException;
        }
    }
}
