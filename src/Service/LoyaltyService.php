<?php

namespace App\Service;

use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Psr\Log\LoggerInterface;

class LoyaltyService
{
    public function __construct(
        private readonly HttpClientInterface $client,
        private readonly ParameterBagInterface $params,
        private readonly LoggerInterface $logger
    ) {
    }

    public function getCard($serialNumber)
    {
        $this->logger->warning('LOYALTY API: Getting card by serial number', [
            'serial_number' => $serialNumber,
            'api_endpoint' => $this->params->get('loyaltyApiEndpoint')
        ]);

        try {
            $response = $this->client->request(
                'GET',
                $this->params->get('loyaltyApiEndpoint') . 'api/v1/cards?serialNumber=' . $serialNumber
            );
            $content = $response->toArray();

            $this->logger->warning('LOYALTY API: Card lookup response', [
                'serial_number' => $serialNumber,
                'total_items' => $content["hydra:totalItems"],
                'status_code' => $response->getStatusCode()
            ]);

            if($content["hydra:totalItems"] == 0){
                $this->logger->warning('LOYALTY API: No card found', ['serial_number' => $serialNumber]);
                return null;
            }

            $card = $content['hydra:member'][0];
            $this->logger->warning('LOYALTY API: Card found', [
                'card_id' => $card['id'],
                'serial_number' => $card['serialNumber'],
                'saldo' => $card['saldo'],
                'is_active' => $card['isActive']
            ]);

            return $card;
        } catch (\Exception $e) {
            $this->logger->error('LOYALTY API: Failed to get card', [
                'serial_number' => $serialNumber,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function updateSaldo($id, $saldo, $cost, $totalSpent, $bonus, $saldoReal)
    {
        $this->logger->warning('LOYALTY API: Starting saldo update', [
            'card_id' => $id,
            'current_saldo' => $saldo,
            'cost' => $cost,
            'total_spent' => $totalSpent,
            'bonus' => $bonus,
            'saldo_real' => $saldoReal
        ]);

        $bonusLeft = $bonus;
        $newTotalSpent = $totalSpent;
        $newSaldoReal = $saldoReal;

        try {
            if ($bonus > 0) {
                $this->logger->warning('LOYALTY API: Processing bonus saldo', ['bonus' => $bonus, 'cost' => $cost]);

                $bonusLeft = $bonus - $cost;
                if ($bonusLeft < 0) {
                    $this->logger->warning('LOYALTY API: Bonus insufficient, using real saldo', [
                        'bonus_shortfall' => abs($bonusLeft),
                        'bonus_left' => $bonusLeft
                    ]);
                    //Since bonusLeft is a negative number, in order to increase newTotalSpent for absoulute value of the bonusLeft,
                    // we need to substract it from newTotalSpent (e.g.newTotalSpent = 5 - (-1) = 6)
                    //The similar logic is applied to newSaldoReal
                    $newTotalSpent = $newTotalSpent - $bonusLeft;
                    $newSaldoReal = $newSaldoReal + $bonusLeft;
                    $bonusLeft = 0;
                }
            } else {
                $this->logger->warning('LOYALTY API: No bonus, using real saldo only');
                $newTotalSpent = $newTotalSpent + $cost;
                $newSaldoReal = $newSaldoReal - $cost;
            }

            $newSaldo = $saldo - $cost;
            $apiUrl = $this->params->get('loyaltyApiEndpoint') . 'api/v1/update-card-saldo/' . $id;
            $requestData = [
                'saldo' => $newSaldo,
                'totalSpent' => $newTotalSpent,
                'saldoBonus' => $bonusLeft,
                'saldoReal' => $newSaldoReal
            ];

            $this->logger->warning('LOYALTY API: Sending saldo update request', [
                'card_id' => $id,
                'api_url' => $apiUrl,
                'request_data' => $requestData,
                'old_saldo' => $saldo,
                'new_saldo' => $newSaldo,
                'amount_deducted' => $cost
            ]);

            $response = $this->client->request(
                'PATCH',
                $apiUrl,
                [
                    'headers' => ['Content-Type' => 'application/merge-patch+json'],
                    'json' => $requestData,
                ]
            );

            $statusCode = $response->getStatusCode();
            $this->logger->warning('LOYALTY API: Saldo update response', [
                'card_id' => $id,
                'status_code' => $statusCode,
                'success' => $statusCode >= 200 && $statusCode < 300
            ]);

            if ($statusCode >= 200 && $statusCode < 300) {
                $this->logger->warning('LOYALTY API: Saldo update successful', [
                    'card_id' => $id,
                    'old_saldo' => $saldo,
                    'new_saldo' => $newSaldo,
                    'amount_deducted' => $cost
                ]);
                return null; // Success
            } else {
                $this->logger->error('LOYALTY API: Saldo update failed with HTTP error', [
                    'card_id' => $id,
                    'status_code' => $statusCode,
                    'response_body' => $response->getContent(false)
                ]);
                return new \Exception('HTTP error: ' . $statusCode);
            }

        } catch (TransportExceptionInterface $transportException) {
            $this->logger->error('LOYALTY API: Transport exception during saldo update', [
                'card_id' => $id,
                'error' => $transportException->getMessage(),
                'trace' => $transportException->getTraceAsString()
            ]);
            return $transportException;
        } catch (\Exception $e) {
            $this->logger->error('LOYALTY API: General exception during saldo update', [
                'card_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $e;
        }
    }
}
