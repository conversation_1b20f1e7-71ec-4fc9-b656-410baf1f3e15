<?php

namespace App\Service;

use Doctrine\DBAL\Connection;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mime\Address;

class DeviceService
{

  public function __construct(private Connection $connection, private readonly EmptyEventEmailSender $emptyEventEmailSender, private readonly LoggerInterface $logger) {}

  public function getByLoggerImei(string $imei){
    return $this->connection->fetchAssociative("SELECT d.* FROM device d JOIN logger l ON d.logger_id = l.id WHERE l.imei = ?", [$imei]);
  }

  public function getDeviceDetailsByImei(string $imei)
  {
    return $this->connection->fetchAssociative("SELECT vdd.* FROM v_device_details vdd WHERE vdd.imei = ?", [$imei]);
  }

  public function getByMachineId(string $machineId)
  {
    return $this->connection->fetchAssociative("SELECT d.*, l.imei FROM device d LEFT JOIN logger l on d.logger_id = l.id WHERE d.machine_id = ?", [$machineId]);
  }

  public function updateSaldo(float $saldo, string $id){
    $updatedAt = date('Y-m-d H:i:s');
    return $this->connection->update("device", ["saldo" => $saldo, "updated_at" => $updatedAt], ["id" => $id]);
  }

  public function updateSaldoAndBillNumber(float $saldo, int $billNumber, string $id){
    $updatedAt = date('Y-m-d H:i:s');
    return $this->connection->update("device", ["saldo" => $saldo, "bill_number" => $billNumber, "updated_at" => $updatedAt], ["id" => $id]);
  }

  public function updateDailySaldo(string $dailySaldo, string $id){
    $updatedAt = date('Y-m-d H:i:s');
    return $this->connection->update("device", ["daily_saldo" => $dailySaldo, "updated_at" => $updatedAt], ["id" => $id]);
  }

  public function updateBillNumber(int $billNumber, string $id){
    $updatedAt = date('Y-m-d H:i:s');
    return $this->connection->update("device", ["bill_number" => $billNumber, "updated_at" => $updatedAt], ["id" => $id]);
  }

  public function updateToken(int $token, string $id){
    $updatedAt = date('Y-m-d H:i:s');
    return $this->connection->update("device", ["token" => $token, "updated_at" => $updatedAt], ["id" => $id]);
  }

  public function processEmpty($imei, $company)
  {
    try {
      $device = $this->getDeviceDetailsByImei($imei);

      $emailData = [
        'imei' => $imei,
        'name' => $device['name'],
        'location' =>  $device['location_name'],
        'tags' => $device['tags']
      ];

      $notificationEmailsDecoded = json_decode($company['notifications_emails'], true);

      $toAddresses = [];
      foreach ($notificationEmailsDecoded['deviceEmptyEventNotifications'] as $emails) {
        array_push($toAddresses, new Address($emails, $emails));
      }
      $this->emptyEventEmailSender->send($toAddresses, $emailData);
    } catch (Exception $e) {
      $this->logger->warning("Empty event email not sent: " . $imei . " " . $e->getMessage(), ["context" => __CLASS__]);
    }
  }

  public function updateDevice(array $valuesToBeUpdated, string $id){
    return $this->connection->update("device", $valuesToBeUpdated, ["id" => $id]);
  }
}
