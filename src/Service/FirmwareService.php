<?php

namespace App\Service;

use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use React\Socket\ConnectionInterface;

class FirmwareService {
  const KINTEIS_URL = 'https://geolux.ams3.digitaloceanspaces.com/smart-toto/toto-kinetis.bin';
  // const TIVA_URL = 'https://geolux.ams3.digitaloceanspaces.com/smart-toto/toto-tiva.bin';
  const TIVA_URL = 'https://noxy-firmware.s3.eu-central-1.amazonaws.com/toto-tiva.bin';

  const KINTEIS_VER_URL = 'https://geolux.ams3.digitaloceanspaces.com/smart-toto/toto-kinetis.ver';
  // const TIVA_VER_URL = 'https://geolux.ams3.digitaloceanspaces.com/smart-toto/toto-tiva.ver';
  const TIVA_VER_URL = 'https://noxy-firmware.s3.eu-central-1.amazonaws.com/toto-tiva.ver';

  private $kinetisFirmware;
  private $kintetisVersion;
  private $kinetisMd5;
  private $tivaFirmware;
  private $tivaVersion;
  private $tivaMd5;

  public function __construct(private HttpClientInterface $httpClient, private LoggerInterface $logger)
  {

  }

  public function getData(){
    return [
      "kinetisFirmware" => $this->kinetisFirmware,
      "kintetisVersion" => $this->kintetisVersion,
      "kinetisMd5" => $this->kinetisMd5,
      "tivaFirmware" => $this->tivaFirmware,
      "tivaVersion" => $this->tivaVersion,
      "tivaMd5" => $this->tivaMd5,
    ];
  }

  public function firmwareUpdate(string $data, ConnectionInterface $socket){
    $fwVersion = null;
    $fwFile = null;
    $isKinetis = false;

    if(in_array($data[0], ['U'])){
      $fwVersion = $this->kintetisVersion;
      $fwFile = $this->kinetisFirmware;
      $isKinetis = true;
    }

    if(in_array($data[0], ['u'])){
      $fwVersion = $this->tivaVersion;
      $fwFile = $this->tivaFirmware;
    }

    if(in_array($data[1], ['?'])){
      if(is_null($fwVersion) or is_null($fwFile)){
        $this->logger->warning("Firmware version or file are null for ? request", ["context" => __CLASS__]);
        return;
      }

      $version = str_replace(["\n", "\r"], "", (string)$fwVersion);
      $size = strlen($fwFile);
      $socket->write("FW " . $version . " " . $size . "#");
      return;
    }

    if(in_array($data[1], ['C'])){
      if(is_null($fwVersion) or is_null($fwFile)){
        $this->logger->warning("Firmware version or file are null for C request", ["context" => __CLASS__]);
        return;
      }

      $data = $fwFile;
      if($isKinetis){
        for ($i = 0x400; $i < 0x410; $i++) {
          $data[$i] = 0xff;
         }
      }

      $crc = $this->genCrc16($data);
      $socket->write("CHK " . $crc . "#");
      return;
    }

    if(in_array($data[1], ['G'])){
      $line = $data;
      $line = substr($line, 3);
      $line = str_replace("\n", "", $line);
      $offset = intval($line);

      if (is_null($fwVersion) or is_null($fwFile)) {
          return;
      }

      $data = $fwFile;

      $len = 512;
      if ($offset + $len > strlen($data)) {
          $len = strlen($data) - $offset;
      }

      if ($len <= 0) {
        $socket->write("#");
        return;
      }

      $hexData = bin2hex(substr($data, $offset, $len));
      $socket->write($hexData . "#");
      return;
    }
  }

  public function loadFirmware(){
    $this->kinetisFirmware = $this->downloadFile(self::KINTEIS_URL);
    if($this->kinetisFirmware){
      $this->kinetisMd5 = md5($this->kinetisFirmware);
    }

    $this->tivaFirmware = $this->downloadFile(self::TIVA_URL);
    if($this->tivaFirmware){
      $this->kinetisMd5 = md5($this->tivaFirmware);
    }

    $this->kintetisVersion = $this->downloadFile(self::KINTEIS_VER_URL);
    $this->tivaVersion = $this->downloadFile(self::TIVA_VER_URL);
  }

  private function downloadFile (string $url){
    try{
      return $this->httpClient->request("GET", $url)->getContent();
    }catch(\Exception $e){
      $this->logger->warning("An error occured during Http Request: " . $e->getMessage(), ["context" => __CLASS__]);
    }
  }

  public function genCrc16($data) {
      $CRC16 = 0x8005;
      $out = 0;
      $bits_read = 0;
      $bit_flag = 0;

      $size = strlen($data); // Use strlen for string length in PHP
      $pos = 0;

      while ($size > 0) {
          $bit_flag = $out >> 15;

          // Get next bit
          $out = ($out << 1) & 0xffff;
          $out |= (ord($data[$pos]) >> $bits_read) & 1; // Working from least significant bits

          // Increment bit counter
          $bits_read++;
          if ($bits_read > 7) {
              $bits_read = 0;
              $pos++;
              $size--;
          }

          // Cycle check
          if ($bit_flag) {
              $out ^= $CRC16;
          }
      }

      // "Push out" the last 16 bits
      for ($i = 0; $i < 16; ++$i) {
          $bit_flag = $out >> 15;
          $out = ($out << 1) & 0xffff;
          if ($bit_flag) {
              $out ^= $CRC16;
          }
      }

      // Reverse the bits
      $crc = 0;
      $i = 0x8000;
      $j = 0x0001;
      while ($i != 0) {
          if ($i & $out) {
              $crc |= $j;
          }
          $i >>= 1;
          $j = ($j << 1) & 0xffff;
      }

      return $crc;
  }
}