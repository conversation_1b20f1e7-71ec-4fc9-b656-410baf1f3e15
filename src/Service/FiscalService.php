<?php

namespace App\Service;

use App\Service\CompanyService;
use App\Service\TransactionService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class FiscalService extends AbstractController
{
    public function __construct(
        private readonly HttpClientInterface    $client,
        public SerializerInterface              $serializer,
        private readonly ParameterBagInterface  $params,
        private readonly EntityManagerInterface $em,
        private readonly TransactionService $transactionService,
        private readonly LoggerInterface $logger,
        private readonly CompanyService $companyService
    ) {}

    public function fiscal(
        array $company,
        string $imei,
        int    $billNumber,
        string $businessSpaceLabel,
        string $isuNumber,
        string $amount,
        string $paymentType,
        ?string $transactionId
    ) {
        // TMP skip fiscal if DEV
        // TODO - set local test endpoint for bills and porezna (porezna has test endpoint too, google)


        if ($this->params->get('appEnv') == 'dev') {
            return true;
        }

        // $certMeta = self::$companies[$company->getOib()];

        // STEP_1: Handle the FINA aplikativni certificate in .pfx format
        // $certificate = $company->getCertificate();
        $fiskal_meta = [
            'company_name' => $company["name"],
            'company_id' => $company["id"],
            'company_oib' => $company["oib"],
            'imei' => $imei,
            'businessSpaceLabel' => $businessSpaceLabel,
            'billNumber' => $billNumber,
            'amount' => $amount,
            'paymentType' => $paymentType,
        ];

        // dump("FISKAL");

        if (!$company["cert_file"]) {
            $e = 'Company does not have certificate';
            $this->logger->warning("No company cert");

            // \Sentry\withScope(static function (\Sentry\State\Scope $scope) use ($fiskal_meta, $e): void {
            //     $scope->setContext('fiskal_meta', $fiskal_meta);
            //     \Sentry\captureMessage($e);
            //     // \Sentry\captureException($e);
            // });
            $this->logger->warning("Fiscal service error: Company does not have certificate - " . $company["id"]);
            return false;
            // throw new \RuntimeException('Company does not have certificate.');
        }

        $XMLRequestType = 'RacunZahtjev'; /* RacunZahtjev OR PoslovniProstorZahtjev */
        // $certificatePass = $certMeta['password'];
        // $pfxCertificate = $this->params->get('certs').$certMeta['oib'].'.pfx';

        openssl_pkcs12_read(
            stream_get_contents($company["cert_file"]),
            $readableOutput,
            $company["cert_pass"]
        );

        $publicCertificate = $readableOutput['cert'];
        $privateKey = $readableOutput['pkey'];

        $privateKeyResource = openssl_pkey_get_private($privateKey, $company["cert_pass"]);
        $publicCertificateData = openssl_x509_parse($publicCertificate);

        // STEP_3: Calculate Zastitni Kod
        $oib = $company["oib"];
        $dt = new \DateTime('now');
        $datumVrijemeIzdavanjaRacuna = $dt->format('d.m.Y H:i:s');
        $brojcanaOznakaRacuna = $billNumber + 1;
        $oznakaPoslovnogProstora = $businessSpaceLabel;
        $oznakaNaplatnogUredaja = $isuNumber;
        $ukupniIznosRacuna = $amount;

        $ZastKodUnsigned = '';

        $ZastKodUnsigned .= $oib;
        $ZastKodUnsigned .= $datumVrijemeIzdavanjaRacuna;
        $ZastKodUnsigned .= $brojcanaOznakaRacuna;
        $ZastKodUnsigned .= $oznakaPoslovnogProstora;
        $ZastKodUnsigned .= $oznakaNaplatnogUredaja;
        $ZastKodUnsigned .= $ukupniIznosRacuna;

        $ZastKodSignature = null;

        openssl_sign($ZastKodUnsigned, $ZastKodSignature, $privateKeyResource, OPENSSL_ALGO_SHA1);

        $ZastKod = md5((string)$ZastKodSignature);

        // prepare bill meta
        $billMeta = [
            'imei' => $imei,
            'jir' => '',
            'zki' => $ZastKod,
            'amount' => $amount,
            'time' => $dt->format('d.m.Y H:i:s'),
            'isuNumber' => $isuNumber,
            'businessSpaceLabel' => $businessSpaceLabel,
            'billNumber' => $billNumber + 1,
            'inVatSystem' => $company["in_vat_system"],
            'paymentType' => $paymentType,
            'isFiscal' => false,
        ];

        // post bill without JIR and isFiscal = false
        $billResponse = self::setBill($billMeta, $company);
        $billContent = json_decode((string)$billResponse->getContent(), null, 512, JSON_THROW_ON_ERROR);
        $bill = $billContent->response;

        if (200 != $billContent->statusCode) {
            // return;
            $this->logger->warning("Fiscal service error: Status code != 200 - " . $company["id"]);
            return false;
            // throw new \RuntimeException('Fiskal error - $billContent->statusCode != 200.');
        }

        $this -> logger -> warning('This is id of created bill' .  $bill->id);

        if(isset($transactionId) && $transactionId) {
          $this -> logger -> warning('Bill is created with id ' .  $bill->id . ' for transaction ' . $transactionId);
        } else {
          $this -> logger -> error('Bill is created without transaction with id ' .  $bill->id);
        }

        // STEP_4: Calculate UUID v4
        $UUID = $this->UUIDv4();

        // STEP_5: Prepare/Build pure request XML (RacunZahtjev OR PoslovniProstorZahtjev)
        $UriId = uniqid();

        if ('RacunZahtjev' == $XMLRequestType) {
            $ns = 'tns';

            $writer = new \XMLWriter();
            $writer->openMemory();

            $writer->setIndent(4);
            $writer->startElementNs($ns, 'RacunZahtjev', 'http://www.apis-it.hr/fin/2012/types/f73');
            $writer->writeAttribute('Id', $UriId);

            $writer->startElementNs($ns, 'Zaglavlje', null);
            $writer->writeElementNs($ns, 'IdPoruke', null, $UUID);
            $writer->writeElementNs($ns, 'DatumVrijeme', null, date('d.m.Y\TH:i:s'));
            $writer->endElement(); /* #Zaglavlje */

            $writer->startElementNs($ns, 'Racun', null);
            $writer->writeElementNs($ns, 'Oib', null, $oib);

            // check if company is in vat system
            if ($company["in_vat_system"]) {
                $writer->writeElementNs($ns, 'USustPdv', null, '1');
            } else {
                $writer->writeElementNs($ns, 'USustPdv', null, '0');
            }

            $writer->writeElementNs($ns, 'DatVrijeme', null, $dt->format('d.m.Y\TH:i:s'));
            $writer->writeElementNs(
                $ns,
                'OznSlijed',
                null,
                'P'
            ); /* P ili N => P na nivou Poslovnog prostora, N na nivou naplatnog uredaja */

            $writer->startElementNs($ns, 'BrRac', null);
            $writer->writeElementNs($ns, 'BrOznRac', null, $brojcanaOznakaRacuna);
            $writer->writeElementNs($ns, 'OznPosPr', null, $oznakaPoslovnogProstora);
            $writer->writeElementNs($ns, 'OznNapUr', null, $oznakaNaplatnogUredaja);
            $writer->endElement(); /* #BrRac */

            // check if company is in vat system and calculate pdv
            if ($company["in_vat_system"]) {
                $osnovica = number_format($ukupniIznosRacuna / 1.25, 2, '.', '');
                $iznos = number_format($ukupniIznosRacuna - ($ukupniIznosRacuna / 1.25), 2, '.', '');

                $writer->startElementNs($ns, 'Pdv', null);
                $writer->startElementNs($ns, 'Porez', null);
                $writer->writeElementNs($ns, 'Stopa', null, '25.00');
                $writer->writeElementNs($ns, 'Osnovica', null, $osnovica);
                $writer->writeElementNs($ns, 'Iznos', null, $iznos);
                $writer->endElement();
                $writer->endElement();
            }

            $writer->writeElementNs($ns, 'IznosUkupno', null, $ukupniIznosRacuna);

            $writer->writeElementNs($ns, 'NacinPlac', null, 'G');
            $writer->writeElementNs($ns, 'OibOper', null, $oib);

            $writer->writeElementNs($ns, 'ZastKod', null, $ZastKod);
            $writer->writeElementNs($ns, 'NakDost', null, '0');

            $writer->endElement(); /* #Racun */

            $writer->endElement(); /* #RacunZahtjev */

            $XMLRequest = $writer->outputMemory();
        }

        // STEP_6: Sign $XMLRequest XML via certificate
        $XMLRequestDOMDoc = new \DOMDocument();
        $XMLRequestDOMDoc->loadXML($XMLRequest);

        $canonical = $XMLRequestDOMDoc->C14N();
        $DigestValue = base64_encode(hash('sha1', $canonical, true));

        $rootElem = $XMLRequestDOMDoc->documentElement;

        $SignatureNode = $rootElem->appendChild(new \DOMElement('Signature'));
        $SignatureNode->setAttribute('xmlns', 'http://www.w3.org/2000/09/xmldsig#');

        $SignedInfoNode = $SignatureNode->appendChild(new \DOMElement('SignedInfo'));
        $SignedInfoNode->setAttribute('xmlns', 'http://www.w3.org/2000/09/xmldsig#');

        $CanonicalizationMethodNode = $SignedInfoNode->appendChild(new \DOMElement('CanonicalizationMethod'));
        $CanonicalizationMethodNode->setAttribute('Algorithm', 'http://www.w3.org/2001/10/xml-exc-c14n#');

        $SignatureMethodNode = $SignedInfoNode->appendChild(new \DOMElement('SignatureMethod'));
        $SignatureMethodNode->setAttribute('Algorithm', 'http://www.w3.org/2000/09/xmldsig#rsa-sha1');

        $ReferenceNode = $SignedInfoNode->appendChild(new \DOMElement('Reference'));
        $ReferenceNode->setAttribute('URI', sprintf('#%s', $UriId));

        $TransformsNode = $ReferenceNode->appendChild(new \DOMElement('Transforms'));

        $Transform1Node = $TransformsNode->appendChild(new \DOMElement('Transform'));
        $Transform1Node->setAttribute('Algorithm', 'http://www.w3.org/2000/09/xmldsig#enveloped-signature');

        $Transform2Node = $TransformsNode->appendChild(new \DOMElement('Transform'));
        $Transform2Node->setAttribute('Algorithm', 'http://www.w3.org/2001/10/xml-exc-c14n#');

        $DigestMethodNode = $ReferenceNode->appendChild(new \DOMElement('DigestMethod'));
        $DigestMethodNode->setAttribute('Algorithm', 'http://www.w3.org/2000/09/xmldsig#sha1');

        $ReferenceNode->appendChild(new \DOMElement('DigestValue', $DigestValue));

        $SignedInfoNode = $XMLRequestDOMDoc->getElementsByTagName('SignedInfo')->item(0);

        $X509Issuer = $publicCertificateData['issuer'];

        $X509IssuerName = sprintf('CN=%s,O=%s,C=%s', $X509Issuer['CN'], $X509Issuer['O'], $X509Issuer['C']);
        $X509IssuerSerial = '1053495513';

        $publicCertificatePureString = str_replace('-----BEGIN CERTIFICATE-----', '', (string)$publicCertificate);
        $publicCertificatePureString = str_replace('-----END CERTIFICATE-----', '', $publicCertificatePureString);

        $SignedInfoSignature = null;

        if (!openssl_sign($SignedInfoNode->C14N(true), $SignedInfoSignature, $privateKeyResource, OPENSSL_ALGO_SHA1)) {
            $this->logger->warning("Fiscal service error: Unable to sign the request - " . $company["id"]);
            return false;
            // throw new \Exception('Unable to sign the request');
        }

        $SignatureNode = $XMLRequestDOMDoc->getElementsByTagName('Signature')->item(0);
        $SignatureValueNode = new \DOMElement('SignatureValue', base64_encode((string)$SignedInfoSignature));
        $SignatureNode->appendChild($SignatureValueNode);

        $KeyInfoNode = $SignatureNode->appendChild(new \DOMElement('KeyInfo'));

        $X509DataNode = $KeyInfoNode->appendChild(new \DOMElement('X509Data'));
        $X509CertificateNode = new \DOMElement('X509Certificate', $publicCertificatePureString);
        $X509DataNode->appendChild($X509CertificateNode);

        $X509IssuerSerialNode = $X509DataNode->appendChild(new \DOMElement('X509IssuerSerial'));

        $X509IssuerNameNode = new \DOMElement('X509IssuerName', $X509IssuerName);
        $X509IssuerSerialNode->appendChild($X509IssuerNameNode);

        $X509SerialNumberNode = new \DOMElement('X509SerialNumber', $X509IssuerSerial);
        $X509IssuerSerialNode->appendChild($X509SerialNumberNode);

        // STEP_7: Add SOAP envelope to signed XML
        $envelope = new \DOMDocument();

        $envelope->loadXML(
            '
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
                    <soapenv:Body></soapenv:Body>
                </soapenv:Envelope>
            '
        );

        $envelope->encoding = 'UTF-8';
        $envelope->version = '1.0';

        $XMLRequestTypeNode = $XMLRequestDOMDoc->getElementsByTagName($XMLRequestType)->item(0);
        $XMLRequestTypeNode = $envelope->importNode($XMLRequestTypeNode, true);

        $envelope->getElementsByTagName('Body')->item(0)->appendChild($XMLRequestTypeNode);

        /* Final, signed XML request */
        $payload = $envelope->saveXML();

        // STEP_8: Execute POST request with signed XML towards CIS
        $ch = curl_init();

        $options = [CURLOPT_URL => $this->params->get('poreznaUrl'), CURLOPT_CONNECTTIMEOUT => 5, CURLOPT_TIMEOUT => 5, CURLOPT_RETURNTRANSFER => true, CURLOPT_POST => true, CURLOPT_POSTFIELDS => $payload, CURLOPT_SSL_VERIFYHOST => 2, CURLOPT_SSL_VERIFYPEER => false];

        curl_setopt_array($ch, $options);

        $response = curl_exec($ch);

        if ($response) {
            $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            $DOMResponse = new \DOMDocument();
            $DOMResponse->loadXML($response);

            if (200 === $code) {
                // For RacunZahtjev
                $time = $DOMResponse->getElementsByTagName('DatumVrijeme')->item(0);
                $Jir = $DOMResponse->getElementsByTagName('Jir')->item(0);

                if (null !== $Jir) {
                    // Update bill
                    self::updateBill(
                        $bill->id,
                        str_replace('"', '', $this->serializer->serialize($Jir->nodeValue, 'json'))
                    );

                    if($transactionId){
                        $this->transactionService->updateTransactionBill($transactionId, $bill->id);
                    }else {
                      $this->logger->error("Transaction bill not updated" . date('Y-m-d H:i:s') . " " . $bill->id . " ");
                    }

                    return [
                        'time' => $this->serializer->serialize($time->nodeValue, 'json'),
                        'jir' => $this->serializer->serialize($Jir->nodeValue, 'json'),
                        'zki' => $ZastKod,
                    ];
                }

                // For RacunZahtjev && PoslovniProstorZahtjev
                echo $response;
            } else {
                $SifraGreske = $DOMResponse->getElementsByTagName('SifraGreske')->item(0);
                $PorukaGreske = $DOMResponse->getElementsByTagName('PorukaGreske')->item(0);

                $this->logger->warning("Fiscal service error" . date('Y-m-d H:i:s') . " " . $bill->id . " " . $transactionId);

                if ($SifraGreske && $PorukaGreske) {
                    // throw new \Exception(sprintf('%s: %s', $SifraGreske->nodeValue, $PorukaGreske->nodeValue));
                    return false;
                } else {
                    // throw new \Exception(sprintf('HTTP response code %s not suited for further actions.', $code));
                    return false;
                }
            }
        } else {
            return false;
            // throw new \Exception(curl_error($ch));
        }

        curl_close($ch);
    }

    // TODO: remove - let messenger handle failed fiscals
    public function subsequentFiscal(
        string $oib,
        int    $billNumber,
        string $businessSpaceLabel,
        string $isuNumber,
        string $amount,
        string $zki,
        string $dateTime,
        string $billId,
        bool   $inVatSystem
    ) {

        if ($this->params->get('appEnv') == 'dev') {
            return true;
        }

        // $certMeta = self::$companies[$oib];
        // $company = $this->em->getRepository(Company::class)->findOneBy(['oib' => $oib]);
        $company = $this->companyService->getByOib($oib);

        $time = strtotime($dateTime);
        $dateFormat = date('d.m.Y\TH:i:s', $time);

        $XMLRequestType = 'RacunZahtjev'; /* RacunZahtjev OR PoslovniProstorZahtjev */

        // STEP_1: Handle the FINA aplikativni certificate in .pfx format
        // $certificate = $company->getCertificate();
        // $certificatePass = $certMeta['password'];
        // $pfxCertificate = $this->params->get('certs').$certMeta['oib'].'.pfx';

        openssl_pkcs12_read(
            stream_get_contents($company["cert_file"]),
            $readableOutput,
            $company["cert_pass"]
        );

        $publicCertificate = $readableOutput['cert'];
        $privateKey = $readableOutput['pkey'];

        $privateKeyResource = openssl_pkey_get_private($privateKey, $company["cert_pass"]);
        $publicCertificateData = openssl_x509_parse($publicCertificate);

        // STEP_3: Calculate Zastitni Kod
        // $oib = $certMeta['oib'];

        $ZastKod = $zki;

        // STEP_4: Calculate UUID v4
        $UUID = $this->UUIDv4();

        // STEP_5: Prepare/Build pure request XML (RacunZahtjev OR PoslovniProstorZahtjev)
        $UriId = uniqid();

        if ('RacunZahtjev' == $XMLRequestType) {
            $ns = 'tns';

            $writer = new \XMLWriter();
            $writer->openMemory();

            $writer->setIndent(4);
            $writer->startElementNs($ns, 'RacunZahtjev', 'http://www.apis-it.hr/fin/2012/types/f73');
            $writer->writeAttribute('Id', $UriId);

            $writer->startElementNs($ns, 'Zaglavlje', null);
            $writer->writeElementNs($ns, 'IdPoruke', null, $UUID);
            $writer->writeElementNs($ns, 'DatumVrijeme', null, $dateFormat);
            $writer->endElement(); /* #Zaglavlje */

            $writer->startElementNs($ns, 'Racun', null);
            $writer->writeElementNs($ns, 'Oib', null, $oib);

            if ($inVatSystem) {
                $writer->writeElementNs($ns, 'USustPdv', null, '1');
            } else {
                $writer->writeElementNs($ns, 'USustPdv', null, '0');
            }

            $writer->writeElementNs($ns, 'DatVrijeme', null, $dateFormat);
            $writer->writeElementNs(
                $ns,
                'OznSlijed',
                null,
                'P'
            ); /* P ili N => P na nivou Poslovnog prostora, N na nivou naplatnog uredaja */

            $writer->startElementNs($ns, 'BrRac', null);
            $writer->writeElementNs($ns, 'BrOznRac', null, $billNumber);
            $writer->writeElementNs($ns, 'OznPosPr', null, $businessSpaceLabel);
            $writer->writeElementNs($ns, 'OznNapUr', null, $isuNumber);
            $writer->endElement(); /* #BrRac */

            if ($inVatSystem) {
                $osnovica = number_format($amount / 1.25, 2, '.', '');
                $iznos = number_format($amount - ($amount / 1.25), 2, '.', '');

                $writer->startElementNs($ns, 'Pdv', null);
                $writer->startElementNs($ns, 'Porez', null);
                $writer->writeElementNs($ns, 'Stopa', null, '25.00');
                $writer->writeElementNs($ns, 'Osnovica', null, $osnovica);
                $writer->writeElementNs($ns, 'Iznos', null, $iznos);
                $writer->endElement();
                $writer->endElement();
            }

            $writer->writeElementNs($ns, 'IznosUkupno', null, number_format($amount, 2, '.', ''));

            $writer->writeElementNs($ns, 'NacinPlac', null, 'G');
            $writer->writeElementNs($ns, 'OibOper', null, $oib);

            $writer->writeElementNs($ns, 'ZastKod', null, $ZastKod);
            $writer->writeElementNs($ns, 'NakDost', null, '0');

            $writer->endElement(); /* #Racun */

            $writer->endElement(); /* #RacunZahtjev */

            $XMLRequest = $writer->outputMemory();
        }

        // STEP_6: Sign $XMLRequest XML via certificate
        $XMLRequestDOMDoc = new \DOMDocument();
        $XMLRequestDOMDoc->loadXML($XMLRequest);

        $canonical = $XMLRequestDOMDoc->C14N();
        $DigestValue = base64_encode(hash('sha1', $canonical, true));

        $rootElem = $XMLRequestDOMDoc->documentElement;

        $SignatureNode = $rootElem->appendChild(new \DOMElement('Signature'));
        $SignatureNode->setAttribute('xmlns', 'http://www.w3.org/2000/09/xmldsig#');

        $SignedInfoNode = $SignatureNode->appendChild(new \DOMElement('SignedInfo'));
        $SignedInfoNode->setAttribute('xmlns', 'http://www.w3.org/2000/09/xmldsig#');

        $CanonicalizationMethodNode = $SignedInfoNode->appendChild(new \DOMElement('CanonicalizationMethod'));
        $CanonicalizationMethodNode->setAttribute('Algorithm', 'http://www.w3.org/2001/10/xml-exc-c14n#');

        $SignatureMethodNode = $SignedInfoNode->appendChild(new \DOMElement('SignatureMethod'));
        $SignatureMethodNode->setAttribute('Algorithm', 'http://www.w3.org/2000/09/xmldsig#rsa-sha1');

        $ReferenceNode = $SignedInfoNode->appendChild(new \DOMElement('Reference'));
        $ReferenceNode->setAttribute('URI', sprintf('#%s', $UriId));

        $TransformsNode = $ReferenceNode->appendChild(new \DOMElement('Transforms'));

        $Transform1Node = $TransformsNode->appendChild(new \DOMElement('Transform'));
        $Transform1Node->setAttribute('Algorithm', 'http://www.w3.org/2000/09/xmldsig#enveloped-signature');

        $Transform2Node = $TransformsNode->appendChild(new \DOMElement('Transform'));
        $Transform2Node->setAttribute('Algorithm', 'http://www.w3.org/2001/10/xml-exc-c14n#');

        $DigestMethodNode = $ReferenceNode->appendChild(new \DOMElement('DigestMethod'));
        $DigestMethodNode->setAttribute('Algorithm', 'http://www.w3.org/2000/09/xmldsig#sha1');

        $ReferenceNode->appendChild(new \DOMElement('DigestValue', $DigestValue));

        $SignedInfoNode = $XMLRequestDOMDoc->getElementsByTagName('SignedInfo')->item(0);

        $X509Issuer = $publicCertificateData['issuer'];

        $X509IssuerName = sprintf('CN=%s,O=%s,C=%s', $X509Issuer['CN'], $X509Issuer['O'], $X509Issuer['C']);
        $X509IssuerSerial = '1053495513';

        $publicCertificatePureString = str_replace('-----BEGIN CERTIFICATE-----', '', (string)$publicCertificate);
        $publicCertificatePureString = str_replace('-----END CERTIFICATE-----', '', $publicCertificatePureString);

        $SignedInfoSignature = null;

        if (!openssl_sign($SignedInfoNode->C14N(true), $SignedInfoSignature, $privateKeyResource, OPENSSL_ALGO_SHA1)) {
            throw new \Exception('Unable to sign the request');
        }

        $SignatureNode = $XMLRequestDOMDoc->getElementsByTagName('Signature')->item(0);
        $SignatureValueNode = new \DOMElement('SignatureValue', base64_encode((string)$SignedInfoSignature));
        $SignatureNode->appendChild($SignatureValueNode);

        $KeyInfoNode = $SignatureNode->appendChild(new \DOMElement('KeyInfo'));

        $X509DataNode = $KeyInfoNode->appendChild(new \DOMElement('X509Data'));
        $X509CertificateNode = new \DOMElement('X509Certificate', $publicCertificatePureString);
        $X509DataNode->appendChild($X509CertificateNode);

        $X509IssuerSerialNode = $X509DataNode->appendChild(new \DOMElement('X509IssuerSerial'));

        $X509IssuerNameNode = new \DOMElement('X509IssuerName', $X509IssuerName);
        $X509IssuerSerialNode->appendChild($X509IssuerNameNode);

        $X509SerialNumberNode = new \DOMElement('X509SerialNumber', $X509IssuerSerial);
        $X509IssuerSerialNode->appendChild($X509SerialNumberNode);

        // STEP_7: Add SOAP envelope to signed XML
        $envelope = new \DOMDocument();

        $envelope->loadXML(
            '
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
                    <soapenv:Body></soapenv:Body>
                </soapenv:Envelope>
            '
        );

        $envelope->encoding = 'UTF-8';
        $envelope->version = '1.0';

        $XMLRequestTypeNode = $XMLRequestDOMDoc->getElementsByTagName($XMLRequestType)->item(0);
        $XMLRequestTypeNode = $envelope->importNode($XMLRequestTypeNode, true);

        $envelope->getElementsByTagName('Body')->item(0)->appendChild($XMLRequestTypeNode);

        /* Final, signed XML request */
        $payload = $envelope->saveXML();

        // STEP_8: Execute POST request with signed XML towards CIS
        $ch = curl_init();

        $options = [CURLOPT_URL => $this->params->get('poreznaUrl'), CURLOPT_CONNECTTIMEOUT => 5, CURLOPT_TIMEOUT => 5, CURLOPT_RETURNTRANSFER => true, CURLOPT_POST => true, CURLOPT_POSTFIELDS => $payload, CURLOPT_SSL_VERIFYHOST => 2, CURLOPT_SSL_VERIFYPEER => false];

        curl_setopt_array($ch, $options);

        $response = curl_exec($ch);

        if ($response) {
            $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            $DOMResponse = new \DOMDocument();
            $DOMResponse->loadXML($response);

            if (200 === $code) {
                // For RacunZahtjev
                $time = $DOMResponse->getElementsByTagName('DatumVrijeme')->item(0);
                $Jir = $DOMResponse->getElementsByTagName('Jir')->item(0);

                if (null !== $Jir) {
                    // Update bill
                    self::updateBill(
                        $billId,
                        str_replace('"', '', $this->serializer->serialize($Jir->nodeValue, 'json'))
                    );

                    return [
                        'time' => $this->serializer->serialize($time->nodeValue, 'json'),
                        'jir' => $this->serializer->serialize($Jir->nodeValue, 'json'),
                        'zki' => $ZastKod,
                    ];
                }

                // For RacunZahtjev && PoslovniProstorZahtjev
                echo $response;
            } else {
                $SifraGreske = $DOMResponse->getElementsByTagName('SifraGreske')->item(0);
                $PorukaGreske = $DOMResponse->getElementsByTagName('PorukaGreske')->item(0);

                if ($SifraGreske && $PorukaGreske) {
                    throw new \Exception(sprintf('%s: %s', $SifraGreske->nodeValue, $PorukaGreske->nodeValue));
                } else {
                    throw new \Exception(sprintf('HTTP response code %s not suited for further actions.', $code));
                }
            }
        } else {
            throw new \Exception(curl_error($ch));
        }

        curl_close($ch);
    }

    public function setBill(array $billMeta, array $company): \Symfony\Component\HttpFoundation\JsonResponse|TransportExceptionInterface|\Exception
    {
        try {
            $response = $this->client->request(
                'POST',
                $this->params->get('billsApiEndpoint'),
                [
                    'headers' => ['Content-Type' => 'application/ld+json'],
                    'json' => [
                        'imei' => $billMeta['imei'],
                        'jir' => '',
                        'zki' => $billMeta['zki'],
                        'oib' => $company['oib'],
                        'amount' => $billMeta['amount'],
                        'time' => str_replace('"', '', (string)$billMeta['time']),
                        'meta' => [
                            'companyName' => $company['name'],
                            'companyOib' => $company['oib'],
                            'companyAddress' => $company['address'],
                            'isu_umber' => $billMeta['isuNumber'],
                            'business_space_label' => $billMeta['businessSpaceLabel'],
                            'bill_number' => $billMeta['billNumber'],
                            'in_vat_system' => $billMeta['inVatSystem'],
                            'payment_type' => $billMeta['paymentType'],
                        ],
                        'isFiscal' => false,
                    ],
                ]
            );

            $content = $response->toArray();

            return $this->json([
                'statusCode' => 200,
                'statusText' => 'ok',
                'response' => $content,
            ]);
        } catch (TransportExceptionInterface $transportException) {
            return $transportException;
        }
    }

    public function updateBill($id, $jir)
    {
        try {
            $this->client->request(
                'PUT',
                $this->params->get('billsApiEndpoint') . '/' . $id,
                [
                    'headers' => ['Content-Type' => 'application/ld+json'],
                    'json' => [
                        'jir' => $jir,
                        'isFiscal' => true,
                    ],
                ]
            );
        } catch (TransportExceptionInterface $transportException) {
            return $transportException;
        }
    }

    public function getBill($id): TransportExceptionInterface|\Exception|array
    {
        try {
            $response = $this->client->request(
                'GET',
                $this->params->get('billsApiEndpoint') . '/' . $id
            );

            $responseContent = $response->toArray();

            return [
                'status' => 'ok',
                'response' => $responseContent,
            ];
        } catch (TransportExceptionInterface $transportException) {
            return $transportException;
        }
    }

    public function UUIDv4(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            random_int(0, 0xFFFF),
            random_int(0, 0xFFFF),
            random_int(0, 0xFFFF),
            random_int(0, 0x0FFF) | 0x4000,
            random_int(0, 0x3FFF) | 0x8000,
            random_int(0, 0xFFFF),
            random_int(0, 0xFFFF),
            random_int(0, 0xFFFF)
        );
    }
}
