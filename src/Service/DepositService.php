<?php

namespace App\Service;

use Doctrine\DBAL\Connection;
use Ramsey\Uuid\Uuid;

class DepositService {
  public function __construct(private Connection $connection)
  {

  }

  public function create($saldo, $deviceId, $token){
    $uuid = Uuid::uuid4();
    $createdAt = date('Y-m-d H:i:s');

    return $this->connection->insert("deposit",
    [
      "id" => $uuid,
      "created_at" => $createdAt,
      "updated_at" => $createdAt,
      "amount" => $saldo,
      "token_amount" => $token,
      "device_id" => $deviceId
    ]);
  }
}
