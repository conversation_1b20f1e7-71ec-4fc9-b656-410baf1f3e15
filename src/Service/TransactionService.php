<?php

namespace App\Service;

use Doctrine\DBAL\Connection;
use Exception;
use Psr\Log\LoggerInterface;
use Ramsey\Uuid\Uuid;

class TransactionService {


  public function __construct(private Connection $connection, private readonly LoggerInterface $logger)
  {
  }

  public function updateTransactionBill(string $transactionId, string $billId) {
    $updatedAt = date('Y-m-d H:i:s');
    return $this->connection->update("transaction", ["bill" => $billId, "updated_at" => $updatedAt], ["id" => $transactionId]);
  }

  public function setTransaction(
    string $amount,
    string $paymentType,
    string $imei,
    string $oib,
    array $device,
    array $location,
    array  $tags,
    string $rfCardSerialNumber
) {
    $uuid = Uuid::uuid4();
    $createdAt = date('Y-m-d H:i:s');

    $locationAddress = json_decode($location["address"], true);

    $tempTags = [];
    if (!empty($tags)) {
        foreach ($tags as $tag) {
            array_push($tempTags, $tag['name']);
        }
    }

   try {
    $this->connection->insert("transaction", [
      "id" => $uuid,
      "created_at" => $createdAt,
      "updated_at" => $createdAt,
      "amount" => $amount,
      "payment_type" => $paymentType,
      "imei" => $imei,
      "oib" => $oib,
      "device" => '/api/v1/devices/' . $device["id"],
      "is_acceptor" => $device["is_acceptor"],
      "is_saldo_reducer" => $device["is_saldo_reducer"],
      "rf_card_serial_number" => $rfCardSerialNumber,
      "device_meta" => json_encode([
        "id" => $device["id"],
        "name" => $device["name"],
        "isuNumber" => $device["isu_number"],
        "businessSpaceLabel" => $device["business_space_label"],
        "isMonsterDevice" => $device["is_monster_device"]
      ]),
      "location_meta" => json_encode([
        "id" => $location["id"],
        "title" => $location["title"],
        "address" => $locationAddress["name"],
        "city" => "",
        "tags" => $tempTags,
        "lessor" => $location["lessor_name"],
      ]),
      "beta_features" => json_encode([]),
      "location" => $location["id"] ? '/api/v1/locations/' . $location["id"] : null,
      "lessor" => $location["lessor_id"] ? '/api/v1/lessors/' . $location["lessor_id"] : null,
      "lessor_meta" => json_encode([
        "name" => $location["lessor_name"]
      ])
      ]);

      $transaction = $this -> getTransactionById($uuid);

      if($transaction) {
       $this -> logger -> warning('Transaction is created with id ' .  $transaction['id']);
      } else {
        $this -> logger -> warning('Transaction is not created ' .  $uuid);
      }

   }catch(Exception $e){
      $this -> logger -> warning('Error in creating transaction' .  $e->getMessage());
   }

    return $uuid;
  }

  public function getTransactionById(string $transactionId)
  {
    return $this->connection->fetchAssociative("SELECT t.* FROM transaction t WHERE t.id = ?", [$transactionId]);
  }

  public function getTransactionsByLocationAndPaymentType($locationId, $paymentType, $limit)
  {
    $locationString = '/api/v1/locations/' . $locationId;
    return $this->connection->fetchAllAssociative(
      "SELECT t.bill FROM transaction t 
      WHERE t.location = :location 
        AND t.payment_type = :payment_type 
      ORDER BY t.created_at DESC 
      LIMIT :limit",
      ['location' => $locationString, 'payment_type' => $paymentType, 'limit' => (int) $limit],
      ['limit' => \PDO::PARAM_INT]
  );
  }
}