<?php

namespace App\Service;

use Doctrine\DBAL\Connection;
use Exception;

class LocationService
{
  public function __construct(private Connection $connection) {}

  public function getById($id)
  {
    return $this->connection->fetchAssociative("SELECT l.*, le.id as lessor_id, le.name as lessor_name, le.address as lessor_address FROM `location` l LEFT JOIN lessor le ON l.lessor_id = le.id WHERE l.id = ?", [$id]);
  }

  public function updateMaintenanceMeta($id, $maintenanceMeta)
  {
    $updatedAt = date('Y-m-d H:i:s');
    $maintenanceMetaToJson = json_encode($maintenanceMeta);
    return $this->connection->update("location", ["maintenance_meta" => $maintenanceMetaToJson, "updated_at" => $updatedAt], ["id" => $id]);
  }
}
