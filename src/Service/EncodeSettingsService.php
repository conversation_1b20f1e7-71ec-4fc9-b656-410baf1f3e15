<?php

namespace App\Service;

class EncodeSettingsService {

  public function encodeSettingsElement(string $result, string $field_code, string $field_type, $data): string {
    $result .= $field_code;
    switch ($field_type) {
        case 'byte':
            $result .= $this->byteToHex($data);
            break;
        case 'short':
            $result .= $this->shortToHex($data);
            break;
        case 'byte_array11':
            $result .= $this->byteArray11ToHex($data);
            break;
        case 'short_array11':
            $result .= $this->shortArray11ToHex($data);
            break;
        case 'string':
            $result .= $this->byteToHex(strlen($data)) . $data;
            break;
        case 'string_list':
            $result .= $this->prepareStringList($data);
            break;
    }
    return $result;
}

private function prepareStringList($data): string {
    $result = '';
    if ($data and isset($data['status'])) {
        if (isset($data['input_values'])) {
            foreach ($data['input_values'] as $key => $value) {
                $result .= $key . ";";
            }
        }
        if (isset($data['output_values'])) {
            foreach ($data['output_values'] as $key => $value) {
                $result .= $key . ";";
            }
        }
        if (isset($data['token_values'])) {
            foreach ($data['token_values'] as $key => $value) {
                $result .= $key . ";";
            }
        }
    }
    return $result . "#";
}

public function byteToHex(int $data): string {
    return bin2hex(pack('C', $data));
}


private function shortToHex(int $data): string {
    return bin2hex(pack('n', $data));
}

private function byteArray11ToHex(array $data): string {
    $arr = array_fill(0, 11, 0);
    for ($i = 0; $i < 11; $i++) {
        $arr[$i] = isset($data[$i]) ? $data[$i] : 0;
    }
    return bin2hex(pack('C*', ...$arr));
}

private function shortArray11ToHex(array $data): string {
    $arr = [];
    for ($i = 0; $i < 11; $i++) {
        $arr[] = isset($data[$i]) ? ($data[$i] >> 8) : 0;
        $arr[] = isset($data[$i]) ? $data[$i] & 0xFF : 0;
    }
    return bin2hex(pack('C*', ...$arr));
}

}