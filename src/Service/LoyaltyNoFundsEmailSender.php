<?php

namespace App\Service;

use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;

class LoyaltyNoFundsEmailSender
{
    public function __construct(private readonly MailerInterface $mailer)
    {
    }

    /**
     * @throws TransportExceptionInterface
     */
    public function send(string $email, array $card): void
    {
        // create email
        $email = (new TemplatedEmail())
            ->from(new Address('<EMAIL>', 'VendingTycoon Loyalty'))
            ->to($email)
            ->subject(sprintf('Obavijest o potrošenim sredstvima na Vašoj loyalty kartici'))
            ->htmlTemplate('email/loyalty/no-funds.html.twig')
            ->context([
                'card' => $card,
            ]);

        // add headers
        $email->getHeaders()
            ->addTextHeader('X-Auto-Response-Suppress', 'OO<PERSON>, DR, RN, NRN, AutoReply')
            ->addTextHeader('List-Unsubscribe', '<mailto:<EMAIL>?subject=unsubscribe>');

        // send email
        $this->mailer->send($email);
    }

    /**
     * @throws TransportExceptionInterface
     */
    public function sendTopUp(string $email, array $card): void
    {
        // create email
        $email = (new TemplatedEmail())
            ->from(new Address('<EMAIL>', 'VendingTycoon Loyalty'))
            ->to($email)
            ->subject(sprintf('Obavijest o stanju loyalty računa'))
            ->htmlTemplate('email/loyalty/card-saldo-low.html.twig')
            ->context([
                'card' => $card,
            ]);

        // add headers
        $email->getHeaders()
            ->addTextHeader('X-Auto-Response-Suppress', 'OOF, DR, RN, NRN, AutoReply')
            ->addTextHeader('List-Unsubscribe', '<mailto:<EMAIL>?subject=unsubscribe>');

        // send email
        $this->mailer->send($email);
    }
}
