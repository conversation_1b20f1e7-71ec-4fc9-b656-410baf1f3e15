<?php

namespace App\Message;


class LoggerMessage {
  public function __construct(
    private string $imei,
    private array  $data,
    private string $id,
    private string $createdAt
  ) {
  }

  public function getImei(): string
  {
    return $this->imei;
  }

  public function getData(): array
  {
    return $this->data;
  }

  public function getId(): string
  {
    return $this->id;
  }

  public function getCreatedAt(): string
  {
    return $this->createdAt;
  }

}