<?php

namespace App\Message;

use App\Message\LoggerMessage;
use App\Service\CompanyService;
use App\Service\DepositService;
use App\Service\DeviceConfigurationService;
use App\Service\DeviceService;
use App\Service\DeviceTagService;
use App\Service\LocationService;
use App\Service\PaymentService;
use Doctrine\DBAL\Connection;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler()]
class LoggerMessageHandler
{
  public function __construct(
    private DeviceService $deviceService,
    private DepositService $depositService,
    private Connection $connection,
    private CompanyService $companyService,
    private DeviceTagService $deviceTagService,
    private LocationService $locationService,
    private PaymentService $paymentService,
    private DeviceConfigurationService $deviceConfigurationService,
    private LoggerInterface $logger,
  ) {}

  public function __invoke(LoggerMessage $message)
  {
    // 1. VALIDATE
    if (!$this->isMessageValid($message)) {
      return;
    }

    try {
      $this->connection->executeQuery("SELECT 1");
    } catch (\Exception $e) {
      $this->connection->close();
      $this->connection->connect();
    }

    // 2. GET DEVICE & COMPANY
    $device = $this->deviceService->getByLoggerImei($message->getImei());
    if (!$device) {
      $this->logger->warning("Device not found for IMEI: " . $message->getImei(), ["context" => __CLASS__]);
      return;
    }

    $deviceConfiguration = $this->deviceConfigurationService->getByImei($message->getImei());

    if (!$deviceConfiguration) {
      $this->logger->warning("Device configuration not found for IMEI: " . $message->getImei(), ["context" => __CLASS__]);
      return;
    }

    $company = $this->companyService->getById($device["company_id"]);

    if (!$company) {
      $this->logger->warning("Company not found for company_id: " . $device["company_id"], ["context" => __CLASS__]);
      return;
    }

    $deviceEventMeta = json_decode($device["event_meta"], true);

    if (!$deviceEventMeta) {
      $this->logger->warning("Not able to decode device meta", ["context" => __CLASS__]);
      return;
    }

    $deviceTags = $this->deviceTagService->getTagsByDeviceId($device["id"]);

    $location = $this->locationService->getById($device["location_id"]);

    $deviceEventMeta['lastMessage'] = $message->getCreatedAt();

    // 3. PROCESS DATA
    $this->connection->beginTransaction();

    try {
      $valuesToBeUpdated = [];
      if ($device["is_problematic"]) {
        $valuesToBeUpdated["is_problematic"] = false;
      }

      $data = $message->getData()[0];

      switch ($data["type"]) {
        case 'on':
          $deviceEventMeta['lastOn'] = $message->getCreatedAt();
          $valuesToBeUpdated["is_critical"] = false;
          break;
        case 'ping':
          $deviceEventMeta['lastPing'] = $message->getCreatedAt();
          $deviceEventMeta['lastPingVersion'] = $data["version"] ?? null;
          $valuesToBeUpdated["is_critical"] = false;
          break;
        case 'empty':
          $deviceEventMeta['lastEmpty'] = $message->getCreatedAt();
          if ($device["saldo"] != 0 or ($device["token"] != 0 and !$device["is_monster_device"])) {
            $this->depositService->create($device["saldo"], $device["id"], $device["is_monster_device"] ? null : $device["token"]);
          }
          if (!$device["is_monster_device"]) {
            $valuesToBeUpdated["token"] = 0;
          }
          $valuesToBeUpdated["saldo"] = 0;
          $this->deviceService->processEmpty($message->getImei(), $company);
          break;
        case 'pay_coin':
          $deviceEventMeta['lastPayCoin'] = $message->getCreatedAt();
          $deviceEventMeta['lastPay'] = $message->getCreatedAt();
          $deviceEventMeta['lastPayType'] = 'pay_coin';
          $this->paymentService->processPayCoin($message->getData(), $device, $deviceConfiguration, $company, $deviceTags, $location);
          $valuesToBeUpdated["is_critical"] = false;

          break;
        case 'pay_cctalk':
          $deviceEventMeta['lastPayCoin'] = $message->getCreatedAt();
          $deviceEventMeta['lastPay'] = $message->getCreatedAt();
          $deviceEventMeta['lastPayType'] = 'pay_cctalk';
          $this->paymentService->processPayCCTalk($message->getData(), $device, $deviceConfiguration, $company, $deviceTags, $location);
          $valuesToBeUpdated["is_critical"] = false;
          break;
        case 'pay_sms':
          $deviceEventMeta['lastPaySms'] = $message->getCreatedAt();
          $deviceEventMeta['lastPay'] = $message->getCreatedAt();
          $deviceEventMeta['lastPayType'] = 'pay_sms';
          $this->paymentService->processPaySMS($message->getData(), $device, $deviceConfiguration, $company, $deviceTags, $location);
          $valuesToBeUpdated["is_critical"] = false;
          break;
        case 'pay_card':
          $deviceEventMeta['lastPayCard'] = $message->getCreatedAt();
          $deviceEventMeta['lastPay'] = $message->getCreatedAt();
          $deviceEventMeta['lastPayType'] = 'pay_card';
          $this->paymentService->processPayCard($message->getData(), $device, $deviceConfiguration, $company, $deviceTags, $location);
          $valuesToBeUpdated["is_critical"] = false;
          break;
        case 'pay_token':
          $deviceEventMeta['lastPayToken'] = $message->getCreatedAt();
          $deviceEventMeta['lastPay'] = $message->getCreatedAt();
          $deviceEventMeta['lastPayType'] = 'pay_token';
          $this->paymentService->processPayToken($message->getData(), $device, $deviceConfiguration, $company, $deviceTags, $location);
          $valuesToBeUpdated["is_critical"] = false;
          break;
        case 'pay_rfcard':
          $deviceEventMeta['lastPayRfCard'] = $message->getCreatedAt();
          $deviceEventMeta['lastPay'] = $message->getCreatedAt();
          $deviceEventMeta['lastPayType'] = 'pay_rfcard';
          $this->paymentService->processPayRfCard($message->getData(), $device, $deviceConfiguration, $company, $deviceTags, $location);
          $valuesToBeUpdated["is_critical"] = false;
          break;
        case 'pay_mdb':
          $deviceEventMeta['lastPayCoin'] = $message->getCreatedAt();
          $deviceEventMeta['lastPay'] = $message->getCreatedAt();
          $deviceEventMeta['lastPayType'] = 'pay_mdb';
          $this->paymentService->processPayMdb($message->getData(), $device, $deviceConfiguration, $company, $deviceTags, $location);
          $valuesToBeUpdated["is_critical"] = false;
          break;
        case 'pay_sci':
          $deviceEventMeta['lastPayCoin'] = $message->getCreatedAt();
          $deviceEventMeta['lastPay'] = $message->getCreatedAt();
          $deviceEventMeta['lastPayType'] = 'pay_sci';
          $this->paymentService->processPaySci($message->getData(), $device, $deviceConfiguration, $company, $deviceTags, $location);
          $valuesToBeUpdated["is_critical"] = false;
          break;
        case 'pay_exec':
          $deviceEventMeta['lastPayCoin'] = $message->getCreatedAt();
          $deviceEventMeta['lastPay'] = $message->getCreatedAt();
          $deviceEventMeta['lastPayType'] = 'pay_exec';
          $this->paymentService->processPayExec($message->getData(), $device, $deviceConfiguration, $company, $deviceTags, $location);
          $valuesToBeUpdated["is_critical"] = false;
          break;
        case 'token_drop':
          $deviceEventMeta['lastTokenDrop'] = $message->getCreatedAt();
          $this->paymentService->processTokenDrop($message->getData(), $device, $deviceConfiguration, $company, $deviceTags, $location);
          $valuesToBeUpdated["is_critical"] = false;
          break;
        default:
          break;
      }

      $device['event_meta'] = json_encode($deviceEventMeta);
      $valuesToBeUpdated["event_meta"] = $device['event_meta'];
      $valuesToBeUpdated["updated_at"] = date('Y-m-d H:i:s');

      $this->deviceService->updateDevice( $valuesToBeUpdated, $device["id"]);

      $this->connection->commit();
    } catch (\Exception $e) {
      $this->logger->warning("Error occured during payment processing: " . $e->getMessage(), ["context" => __CLASS__, 'trace' => $e->getTraceAsString(), 'exception' => $e]);
      $this->connection->rollBack();
      throw $e;
    }
  }

  private function isMessageValid(LoggerMessage $message)
  {
    $data = $message->getData();
    $imei = $message->getImei();

    if (!$data || !$imei) {
      return false;
    }

    return true;
  }
}
