<?php

namespace App\Message;

class NayaxMessage {
  public function __construct(
    private string $machineId,
    private float  $payedValue,
    private array $createdDate,
  ) {
  }

  public function getMachineId(): string
  {
    return $this->machineId;
  }

  public function getPayedValue(): float
  {
    return $this->payedValue;
  }

  public function getCreatedDate(): array
  {
    return $this->createdDate;
  }
}