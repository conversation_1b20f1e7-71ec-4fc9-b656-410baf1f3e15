<?php

namespace App\Message;

use App\Service\CompanyService;
use App\Service\DeviceConfigurationService;
use App\Service\DeviceService;
use App\Service\DeviceTagService;
use App\Service\LocationService;
use App\Service\PaymentService;
use Doctrine\DBAL\Connection;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler()]
class NayaxMessageHandler
{
  public function __construct(
    private DeviceService $deviceService,
    private LoggerInterface $logger,
    private PaymentService $paymentService,
    private CompanyService $companyService,
    private DeviceConfigurationService $deviceConfigurationService,
    private DeviceTagService $deviceTagService,
    private LocationService $locationService,
    private Connection $connection,
  ) {}

  public function __invoke(NayaxMessage $nayaxMessage)
  {
    $timestamp = $nayaxMessage->getCreatedDate()['epochSecond'];
    $machineId = $nayaxMessage->getMachineId();
    $payedValue = $nayaxMessage->getPayedValue();

    $this->logger->warning("Nayax message handler " . $machineId, ["context" => __CLASS__]);

    if (!$this->isMessageValid($nayaxMessage)) {
      $this->logger->warning("Nayax message is not valid " . $machineId, ["context" => __CLASS__]);
      return null;
    }

    try {
      $this->connection->executeQuery("SELECT 1");
    } catch (\Exception $e) {
      $this->connection->close();
      $this->connection->connect();
    }

    $device = $this->deviceService->getByMachineId($machineId);

    if (!$device) {
      $this->logger->warning("Device not found for Machine ID: " . $machineId, ["context" => __CLASS__]);
      return null;
    }

    $deviceConfiguration = $this->deviceConfigurationService->getByImei($device["imei"]);

    if (!$deviceConfiguration) {
      $this->logger->warning("Device configuration not found for IMEI: " . $device["imei"], ["context" => __CLASS__]);
      return;
    }

    $company = $this->companyService->getById($device["company_id"]);

    if (!$company) {
      $this->logger->warning("Company not found for company_id: " . $device["company_id"], ["context" => __CLASS__]);
      return;
    }

    $deviceEventMeta = json_decode($device["event_meta"], true);

    if (!$deviceEventMeta) {
      $this->logger->warning("Not able to decode device meta", ["context" => __CLASS__]);
      return;
    }

    $deviceTags = $this->deviceTagService->getTagsByDeviceId($device["id"]);

    $location = $this->locationService->getById($device["location_id"]);

    $createdAtDate = new \DateTime("@$timestamp");
    $createdAt = $createdAtDate->format('Y-m-d H:i:s');

    $deviceEventMeta['lastMessage'] = $createdAt;

    $this->connection->beginTransaction();

    try {
      $valuesToBeUpdated = [];

      if ($device["is_problematic"]) {
        $valuesToBeUpdated["is_problematic"] = false;
      }

      $deviceEventMeta['lastPayCard'] = $createdAt;
      $deviceEventMeta['lastPay'] = $createdAt;
      $deviceEventMeta['lastPayType'] = 'pay_card';
      $this->paymentService->processPayNayaxCard($payedValue, $device, $deviceConfiguration, $company, $deviceTags, $location);
      $valuesToBeUpdated["is_critical"] = false;

      $device['event_meta'] = json_encode($deviceEventMeta);
      $valuesToBeUpdated["event_meta"] = $device['event_meta'];
      $valuesToBeUpdated["updated_at"] = date('Y-m-d H:i:s');

      $this->deviceService->updateDevice($valuesToBeUpdated, $device["id"]);
      $this->connection->commit();
    } catch (\Exception $e) {
      $this->logger->warning("Error occured during payment processing: " . $e->getMessage(), ["context" => __CLASS__, 'trace' => $e->getTraceAsString(), 'exception' => $e]);
      $this->connection->rollBack();
      throw $e;
    }

    return;
  }

  private function isMessageValid(NayaxMessage $nayaxMessage)
  {
    $machineId = $nayaxMessage->getMachineId();
    $payedValue = $nayaxMessage->getPayedValue();

    if (!$machineId) {
      $this->logger->warning("Machine ID not present in the message", ["context" => __CLASS__]);
      return false;
    }

    if (!$payedValue) {
      $this->logger->warning("PayedValue not present in the message", ["context" => __CLASS__]);
      return false;
    }

    return true;
  }
}
