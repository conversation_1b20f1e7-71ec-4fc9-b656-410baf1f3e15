{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.1", "ext-ctype": "*", "ext-iconv": "*", "async-aws/sqs": "^2.1", "aws/aws-sdk-php": "^3.324", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.12", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/orm": "^3.2", "league/html-to-markdown": "^5.1", "phpnexus/cwh": "^3.1", "ramsey/uuid": "^4.7", "ramsey/uuid-doctrine": "^2.1", "react/socket": "^1.15", "symfony/amazon-sqs-messenger": "6.4.*", "symfony/console": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/flex": "^2", "symfony/framework-bundle": "6.4.*", "symfony/mailer": "6.4.*", "symfony/messenger": "6.4.*", "symfony/monolog-bundle": "^3.10", "symfony/property-access": "6.4.*", "symfony/runtime": "6.4.*", "symfony/security-bundle": "6.4.*", "symfony/serializer": "6.4.*", "symfony/twig-bundle": "6.4.*", "symfony/yaml": "6.4.*", "twig/cssinliner-extra": "^3.13", "twig/inky-extra": "^3.13", "twig/markdown-extra": "^3.13"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.4.*"}}, "require-dev": {"symfony/maker-bundle": "^1.60", "symfony/stopwatch": "6.4.*", "symfony/web-profiler-bundle": "6.4.*"}}