{% extends 'base.html.twig' %}

{% block title %}Noxy{% endblock %}

{% block body %}
    <style>
        .wrapper { margin: 1em auto; max-width: 400px; font: 18px/1.5 sans-serif; }
        .input-wrapper {display: flex; flex-direction: column; margin-bottom: 8px}
        .input-field {height: 28px; border-radius: 4px; border: 1px solid grey; padding: 2px 8px;}
        .button {
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            background: #197DE4;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .alert-danger { color: red; }
    </style>
    <div class="wrapper">
        <form method="post">
            {% if error %}
                <div class="alert alert-danger">{{ error.messageKey|trans(error.messageData, 'security') }}</div>
            {% endif %}

            <div class="input-wrapper">
                <label for="username"><small>Email</small></label>
                <input type="email" value="{{ last_username }}" name="_username" id="username" class="form-control input-field" autocomplete="email" required autofocus>
            </div>
            <div class="input-wrapper">
                <label for="password"><small>Password</small></label>
                <input type="password" name="_password" id="password" class="form-control input-field" autocomplete="current-password" required>
            </div>

            {# <input type="hidden" name="_csrf_token"
                value="{{ csrf_token('authenticate') }}"
            > #}


                {# Uncomment this section and add a remember_me option below your firewall to activate remember me functionality.
                See https://symfony.com/doc/current/security/remember_me.html #}

                {# <div class="checkbox mb-3">
                    <input type="checkbox" name="_remember_me" id="_remember_me">
                    <label for="_remember_me">Remember me</label>
                </div> #}


            <button class="button" type="submit">
                Sign in
            </button>
        </form>
    </div>
{% endblock %}
