{% extends 'base.html.twig' %}

{% block title %}Noxy{% endblock %}

{% block body %}
<style>
    .wrapper { margin: 1em auto; max-width: 400px; font: 18px/1.5 sans-serif; }
    .input-wrapper {display: flex; flex-direction: column; margin-bottom: 8px; margin-top: 40px; gap: 8px}
    .input-field {height: 28px; border-radius: 4px; border: 1px solid grey; padding: 2px 8px;}
    .button {
        text-decoration: none;
        padding: 8px 16px;
        border-radius: 4px;
        background: #197DE4;
        color: white;
        font-size: 14px;
        border: none;
        cursor: pointer;
    }
    .user { margin-bottom: 8px; }
    .device-wrapper { margin-top: 60px; }
    .device-name { margin-bottom: 0; }
    .device-cta-wrapper { display: flex; gap: 8px; margin-top: 12px; }
    .hidden-input { display: none; }
    #response-message {
        margin-top: 48px;
        padding: 24px;
        border-radius: 4px;
    }
</style>
<div class="wrapper">
    <h2>Noxy Interface</h2>
    {% if app.user %}
        <div class="user">You are logged in as <b>{{ app.user.userIdentifier }}</b></div>
        <a class="button" href="{{ path('app_logout') }}">Logout</a>

        <div class="input-wrapper">
            <form id="reload-firmware">
                <span>Load the latest firmware</span>
                <button class="button" type="submit">
                    Reload Firmware
                </button>
            </form>
        </div>

        <form id="start-game-sms-code">
            <div class="input-wrapper">
                <label for="sms-code"><small>Free Ride</small></label>
                <input placeholder="Code" type="number" name="code" id="sms-code" class="form-control input-field">
                <button class="button" type="submit">
                    Start Game
                </button>
            </div>
        </form>


        <form method="GET">
            <div class="input-wrapper">
                <label for="imei"><small>Look for a device by IMEI</small></label>
                <input placeholder="IMEI" type="number" name="imei" id="imei" class="form-control input-field">
                {% if error is defined %}
                    <span>{{error}}</span>
                {% endif %}

                <button class="button" type="submit">
                    Search
                </button>
            </div>
        </form>
        {% if (error is not defined) and (version is defined) %}
            <div class="device-wrapper">
                <h2 class="device-name">{{name}}</h2>
                <div><small><b>Business Space Label:</b> {{business_space_label}}</small></div>
                <div><small><b>IMEI:</b> {{imei}}</small></div>
                <div><small><b>Firmware version:</b> {{version}}</small></div>
                <div><small><b>Last ping:</b> {{lastPing}}</small></div>

                    <div class="device-cta-wrapper">
                        <form id="cta-form-update">
                            <input type="text" name="imei" value={{imei}} class="hidden-input" />
                            <button class="button" type="submit">Update firmware</button>
                        </form>
                        <form id="cta-form-start-game">
                            <input type="text" name="imei" value={{imei}} class="hidden-input" />
                            <button class="button" type="submit">Start game</button>
                        </form>
                    </div>
            </div>
        {% endif %}
    {% endif %}
    <div id="response-message"></div>
</div>
<script>
    function sendRequest(event, url, data, method) {
        event.preventDefault();

        const formData = new FormData(data);
        const infoElement = document.getElementById('response-message');

        fetch(url, {
            method,
            body: formData,
        })
        .then(response => response.json())
        .then(data => {
            if(data.message){
                infoElement.innerText = data.message;
                infoElement.style.backgroundColor = "rgba(25, 125, 228, 0.25)";
            }
            if(data.error){
                infoElement.innerText = data.error;
                infoElement.style.backgroundColor = 'rgba(238, 39, 55, 0.75)';
            }
        })
        .catch(error => {
            infoElement.innerText = error;
            infoElement.style.backgroundColor = 'rgba(238, 39, 55, 0.25)';
        });
    }

    document.getElementById('reload-firmware').addEventListener('submit', function(event) {
        sendRequest(event, '{{ path('reload_firmware') }}', this, 'POST');
    });
    document.getElementById('start-game-sms-code').addEventListener('submit', function(event) {
        sendRequest(event, '{{ path('manual_start_game') }}', this, 'POST');
    });
    document.getElementById('cta-form-update').addEventListener('submit', function(event) {
        sendRequest(event, '{{ path('update_device') }}', this, 'POST');
    });
    document.getElementById('cta-form-start-game').addEventListener('submit', function(event) {
        sendRequest(event, '{{ path('manual_start_game') }}', this, 'POST');
    });
</script>
{% endblock %}
