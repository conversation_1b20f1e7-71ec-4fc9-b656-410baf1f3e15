name: Deploy to Amazon ECS (message-unique-id-noxy)
on:
  push:
    branches: [ "message-unique-id" ]

env:
  AWS_REGION: eu-central-1                                       # Set this to your preferred AWS region, e.g. us-west-1
  ECR_REPOSITORY: message-unique-id-noxy                                       # Set this to your Amazon ECR repository name for message-unique-id-noxy
  ECS_SERVICE: message-unique-id-noxy-app                                      # Set this to your Amazon ECS service name for message-unique-id-noxy
  ECS_CLUSTER: message-unique-id-noxy                                          # Set this to your Amazon ECS cluster name
  ECS_TASK_DEFINITION: ./.aws/ecs-task-definition-message-unique-id-noxy.json       # Path to message-unique-id-noxy task definition
  CONTAINER_NAME: message-unique-id-noxy-container                             # Set this to the name of the container in the ECS task definition for message-unique-id-noxy
  SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
permissions:
  contents: read

jobs:
  deploy:
    name: Deploy (message-unique-id-noxy)
    runs-on: ubuntu-latest
    environment: message-unique-id-noxy
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Modify .env file for message-unique-id-noxy
        run: |
          sed -i 's|APP_ENV=prod|APP_ENV=message-unique-id-noxy|g' .env
          echo "Environment set to message-unique-id-noxy"

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: message-unique-id-noxy-latest
        run: |
          # Build a docker container and push it to ECR so that it can be deployed to ECS.
          echo "SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN" > .env.ci
          echo "Building Docker image..."
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          echo "Pushing Docker image to ECR..."
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_ENV

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.ECS_TASK_DEFINITION }}
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ env.image }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ env.ECS_SERVICE }}
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true
