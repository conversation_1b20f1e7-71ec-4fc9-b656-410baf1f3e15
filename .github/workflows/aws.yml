name: Deploy to Amazon ECS

on:
  push:
    branches: [ "prod" ]

env:
  AWS_REGION: eu-central-1                                # set this to your preferred AWS region, e.g. us-west-1
  ECR_REPOSITORY: noxy                                    # set this to your Amazon ECR repository name
  ECS_SERVICE: noxy-app                                   # set this to your Amazon ECS service name
  ECS_CLUSTER: noxy                                       # set this to your Amazon ECS cluster name
  ECS_TASK_DEFINITION: ./.aws/ecs-task-definition.json    # set this to the path to your Amazon ECS task definition
  CONTAINER_NAME: noxy-container                          # set this to the name of the container in the ECS task definition
  SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}

permissions:
  contents: read

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: Production

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Modify .env file based on environment
        run: |
          if [ "${{ github.ref }}" == "refs/heads/prod" ]; then
            sed -i 's|APP_ENV=dev|APP_ENV=prod|g' .env
            echo "Environment set to production"
          else
            sed -i 's|APP_ENV=prod|APP_ENV=dev|g' .env
            echo "Environment set to development"
          fi

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: latest
        run: |
          # Build a docker container and push it to ECR so that it can be deployed to ECS.
          echo "SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN" > .env.ci
          echo "Building Docker image..."
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          echo "Pushing Docker image to ECR..."
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_ENV

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.ECS_TASK_DEFINITION }}
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ env.image }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ env.ECS_SERVICE }}
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true
