framework:
  messenger:
    # Uncomment this (and the failed transport below) to send failed messages to this transport for later handling.
    failure_transport: failed

    serializer:
      default_serializer: messenger.transport.symfony_serializer
      symfony_serializer:
        format: json
        context: {}

    transports:
      # https://symfony.com/doc/current/messenger.html#transport-configuration
      # async: '%env(MESSENGER_TRANSPORT_DSN)%'
      # failed: 'doctrine://default?queue_name=failed'
      # sync: 'sync://'
      async:
        dsn: "%env(SQS_BASE_URL)%/%env(SQS_ACCOUNT)%/%env(Q_NAME)%?access_key=%env(SQS_ACCESS_KEY)%&secret_key=%env(SQS_SECRET)%"
        serializer: messenger.transport.symfony_serializer
      nayax:
        dsn: "%env(SQS_BASE_URL)%/%env(SQS_ACCOUNT)%/%env(NAYAX_Q_NAME)%?access_key=%env(SQS_ACCESS_KEY)%&secret_key=%env(SQS_SECRET)%"
        serializer: App\Serializer\NayaxMessageSerializer

      failed: "doctrine://default?queue_name=failed&auto_setup=1"

    routing:
      # Route your messages to the transports
      'App\Message\LoggerMessage': async
      'App\Message\NayaxMessage': nayax
# when@test:
#    framework:
#        messenger:
#            transports:
#                # replace with your transport name here (e.g., my_transport: 'in-memory://')
#                # For more Messenger testing tools, see https://github.com/zenstruck/messenger-test
#                async: 'in-memory://'
