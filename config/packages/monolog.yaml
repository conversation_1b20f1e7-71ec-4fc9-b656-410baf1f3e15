monolog:
  channels:
    - deprecation # Deprecations are logged in the dedicated "deprecation" channel when it exists

when@dev:
  monolog:
    handlers:
      main:
        type: stream
        action_level: warning
        # handler: custom
        # excluded_http_codes: [ 404, 405 ]
        buffer_size: 0 # How many messages should be saved? Prevent memory leaks
      # custom:
      #   type: service
      #   id: cloudwatch_handler
      # main:
      #   type: stream
      #   #        path: "%kernel.logs_dir%/%kernel.environment%.log"
      #   path: php://stderr
      #   level: debug
      #   channels: [ "!event" ]
      # #      std:
      # #        type: stream
      # #        level: debug
      # #        path: php://stderr

      # # uncomment to get logging in your browser
      # # you may have to allow bigger header sizes in your Web server configuration
      # #firephp:
      # #    type: firephp
      # #    level: info
      # #chromephp:
      # #    type: chromephp
      # #    level: info
      # console:
      #   type: console
      #   process_psr_3_messages: false
      #   channels: [ "!event", "!doctrine", "!console" ]

when@test:
  monolog:
    handlers:
      main:
        type: stream
        action_level: warning
        handler: nested
        excluded_http_codes: [ 404, 405 ]
        channels: [ "!event" ]
      nested:
        type: stream
        path: "%kernel.logs_dir%/%kernel.environment%.log"
        level: debug

when@prod:
  monolog:
    handlers:
      main:
        type: stream
        action_level: warning
        handler: custom
        # excluded_http_codes: [ 404, 405 ]
        buffer_size: 0 # How many messages should be saved? Prevent memory leaks
      custom:
        type: service
        id: cloudwatch_handler